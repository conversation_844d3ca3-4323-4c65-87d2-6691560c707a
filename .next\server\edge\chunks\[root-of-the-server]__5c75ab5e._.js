(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__5c75ab5e._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/lib/supabase/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "updateSession": (()=>updateSession)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
;
;
async function updateSession(request) {
    let supabaseResponse = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next({
        request
    });
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createServerClient"])(("TURBOPACK compile-time value", "https://ocyjxnddxuhhlnguybre.supabase.co"), ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9jeWp4bmRkeHVoaGxuZ3V5YnJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5NDY2ODYsImV4cCI6MjA2NzUyMjY4Nn0.LQJjfsT5lKAnfD7dvWMtKMjgLiFo-vjcone8yi-Gm40"), {
        cookies: {
            getAll () {
                return request.cookies.getAll();
            },
            setAll (cookiesToSet) {
                cookiesToSet.forEach(({ name, value, options })=>request.cookies.set(name, value));
                supabaseResponse = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next({
                    request
                });
                cookiesToSet.forEach(({ name, value, options })=>supabaseResponse.cookies.set(name, value, options));
            }
        }
    });
    // IMPORTANT: Avoid writing any logic between createServerClient and
    // supabase.auth.getUser(). A simple mistake could make it very hard to debug
    // issues with users being randomly logged out.
    const { data: { user } } = await supabase.auth.getUser();
    if (!user && !request.nextUrl.pathname.startsWith('/login') && !request.nextUrl.pathname.startsWith('/signup') && !request.nextUrl.pathname.startsWith('/auth')) {
        // no user, potentially respond by redirecting the user to the login page
        const url = request.nextUrl.clone();
        url.pathname = '/login';
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
    }
    // IMPORTANT: You *must* return the supabaseResponse object as it is. If you're
    // creating a new response object with NextResponse.next() make sure to:
    // 1. Pass the request in it, like so:
    //    const myNewResponse = NextResponse.next({ request })
    // 2. Copy over the cookies, like so:
    //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
    // 3. Change the myNewResponse object instead of the supabaseResponse object
    return supabaseResponse;
}
}}),
"[project]/src/i18n/messages/de.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"common\":{\"loading\":\"Laden...\",\"error\":\"Fehler\",\"success\":\"Erfolg\",\"cancel\":\"Abbrechen\",\"save\":\"Speichern\",\"delete\":\"Löschen\",\"edit\":\"Bearbeiten\",\"close\":\"Schließen\",\"back\":\"Zurück\",\"next\":\"Weiter\",\"previous\":\"Vorherige\",\"search\":\"Suchen\",\"filter\":\"Filtern\",\"sort\":\"Sortieren\",\"export\":\"Exportieren\",\"import\":\"Importieren\"},\"navigation\":{\"home\":\"Startseite\",\"about\":\"Über uns\",\"features\":\"Funktionen\",\"pricing\":\"Preise\",\"contact\":\"Kontakt\",\"privacy\":\"Datenschutz\",\"terms\":\"Bedingungen\",\"dashboard\":\"Dashboard\",\"tasks\":\"Aufgaben\",\"budget\":\"Budget\",\"shopping\":\"Einkaufen\",\"chat\":\"KI Chat\",\"recipes\":\"Rezepte\",\"login\":\"Anmelden\",\"signup\":\"Registrieren\",\"logout\":\"Abmelden\"},\"hero\":{\"badge\":\"KI-gestützte Lebensführung\",\"title\":\"Verwalten Sie Ihr Leben\",\"subtitle\":\"Die All-in-One-Plattform zur Organisation Ihrer Aufgaben, Verfolgung Ihres Budgets, Verwaltung von Einkaufslisten und KI-gestützte Unterstützung für bessere Lebensführung.\",\"cta_primary\":\"Heute kostenlos starten\",\"cta_secondary\":\"Demo ansehen\",\"trust_users\":\"2.5M+ Benutzer\",\"trust_rating\":\"4.9/5 Bewertung\",\"trust_security\":\"Unternehmenssicherheit\",\"scroll_indicator\":\"Scrollen zum Erkunden\"},\"features\":{\"title\":\"Alles was Sie brauchen an einem Ort\",\"subtitle\":\"Optimieren Sie Ihr tägliches Leben mit unserer umfassenden Suite von Management-Tools.\",\"task_management\":{\"title\":\"Intelligente Aufgabenverwaltung\",\"description\":\"KI-gestützte Aufgabenorganisation mit Drag-and-Drop, Prioritäten und intelligenter Planung.\",\"stats\":\"50K+ täglich\"},\"budget_tracking\":{\"title\":\"Erweiterte Budgetverfolgung\",\"description\":\"Echtzeit-Ausgabenüberwachung mit prädiktiver Analytik und automatisierter Kategorisierung.\",\"stats\":\"98% Genauigkeit\"},\"shopping_lists\":{\"title\":\"Intelligente Einkaufslisten\",\"description\":\"Smarte Einkaufslisten mit Preisverfolgung, Geschäftsoptimierung und Familienfreigabe.\",\"stats\":\"20% sparen\"},\"ai_chat\":{\"title\":\"KI-Lebensassistent\",\"description\":\"Personalisierte Empfehlungen und Einblicke durch fortgeschrittenes maschinelles Lernen.\",\"stats\":\"24/7 verfügbar\"},\"recipes\":{\"title\":\"Rezeptverwaltung\",\"description\":\"Auto-Import von Rezepten, Nährwertanalyse und Mahlzeitenplanung mit Ernährungspräferenzen.\",\"stats\":\"10K+ Rezepte\"},\"security\":{\"title\":\"Sicherheit & Datenschutz\",\"description\":\"Ende-zu-Ende-Verschlüsselung mit Echtzeit-Synchronisation auf allen Ihren Geräten weltweit.\",\"stats\":\"Bankgrad\"}},\"testimonials\":{\"title\":\"Weltweit von Benutzern geliebt\",\"subtitle\":\"Sehen Sie, was unsere Community über die Transformation ihres täglichen Lebens sagt.\"},\"stats\":{\"title\":\"Global vertraut\",\"subtitle\":\"Schließen Sie sich Millionen von Benutzern an, die ihre Produktivität und Lebensführung transformiert haben.\",\"users\":\"Aktive Benutzer weltweit\",\"countries\":\"Unterstützte Länder\",\"tasks\":\"Täglich erledigte Aufgaben\",\"satisfaction\":\"Kundenzufriedenheit\",\"growing\":\"Täglich wachsend\",\"global\":\"Globale Reichweite\",\"productivity\":\"Produktivitätsschub\",\"happy\":\"Glückliche Benutzer\"},\"pricing\":{\"title\":\"Wählen Sie Ihren Plan\",\"subtitle\":\"Starten Sie kostenlos und erweitern Sie beim Wachsen. Alle Pläne enthalten unsere Kernfunktionen.\",\"monthly\":\"Monatlich\",\"yearly\":\"Jährlich\",\"save\":\"20% sparen\",\"most_popular\":\"Am beliebtesten\",\"free\":{\"name\":\"Kostenlos\",\"description\":\"Perfekt für den Einstieg\",\"cta\":\"Kostenlos starten\"},\"pro\":{\"name\":\"Pro\",\"description\":\"Für Power-User und Familien\",\"cta\":\"Pro-Test starten\"},\"enterprise\":{\"name\":\"Unternehmen\",\"description\":\"Für Organisationen und Teams\",\"cta\":\"Vertrieb kontaktieren\"},\"comparison\":\"Detaillierten Vergleich anzeigen\",\"guarantee\":\"30-Tage-Geld-zurück-Garantie\"},\"cta\":{\"title\":\"Bereit die Kontrolle zu übernehmen?\",\"subtitle\":\"Schließen Sie sich Tausenden von Benutzern an, die ihre täglichen Routinen mit LifeManager transformiert haben.\",\"button\":\"Kostenlos starten\"},\"footer\":{\"tagline\":\"Lebensführung vereinfachen, eine Aufgabe nach der anderen. Schließen Sie sich der Revolution des organisierten Lebens an.\",\"copyright\":\"© 2024 LifeManager. Alle Rechte vorbehalten. Mit ❤️ für Produktivitätsenthusiasten weltweit gemacht.\"},\"contact\":{\"title\":\"Wir sind hier um zu helfen\",\"subtitle\":\"Haben Sie Fragen zu LifeManager? Brauchen Sie Hilfe beim Einstieg? Unser freundliches Support-Team ist bereit, Sie bei jedem Schritt zu unterstützen.\",\"form\":{\"title\":\"Senden Sie uns eine Nachricht\",\"name\":\"Name\",\"email\":\"E-Mail\",\"company\":\"Unternehmen (Optional)\",\"subject\":\"Betreff\",\"message\":\"Nachricht\",\"type\":\"Anfrage-Typ\",\"send\":\"Nachricht senden\",\"success\":\"Nachricht gesendet!\",\"success_message\":\"Wir werden Ihnen innerhalb von 24 Stunden antworten.\"},\"methods\":{\"email\":{\"title\":\"E-Mail-Support\",\"description\":\"Hilfe per E-Mail erhalten\",\"response\":\"Innerhalb von 24 Stunden\"},\"chat\":{\"title\":\"Live-Chat\",\"description\":\"Chatten Sie mit unserem Team\",\"response\":\"Sofortige Antwort\"},\"phone\":{\"title\":\"Telefon-Support\",\"description\":\"Unternehmenskunden\",\"response\":\"Geschäftszeiten\"}}},\"about\":{\"title\":\"Das Leben vereinfachen, eine Aufgabe nach der anderen\",\"subtitle\":\"2019 gegründet, entstand LifeManager aus einem einfachen Glauben: Jeder verdient Werkzeuge, die das tägliche Leben einfacher machen, nicht komplizierter.\",\"mission\":{\"title\":\"Unsere Mission\",\"description\":\"Wir glauben, dass Technologie das menschliche Potenzial verstärken sollte, nicht überwältigen. Unsere Mission ist es, intuitive, mächtige Werkzeuge zu schaffen, die Menschen helfen, ihr Leben zu organisieren, ihre Ziele zu erreichen und eine bessere Work-Life-Balance zu finden.\"},\"values\":{\"title\":\"Unsere Werte\",\"subtitle\":\"Die Prinzipien, die alles leiten, was wir tun\"},\"team\":{\"title\":\"Lernen Sie unser Team kennen\",\"subtitle\":\"Die leidenschaftlichen Menschen hinter LifeManager\"},\"journey\":{\"title\":\"Unsere Reise\",\"subtitle\":\"Wichtige Meilensteine in unserer Mission, die Lebensführung zu vereinfachen\"}}}"));}}),
"[project]/src/i18n/messages/en.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"common\":{\"loading\":\"Loading...\",\"error\":\"Error\",\"success\":\"Success\",\"cancel\":\"Cancel\",\"save\":\"Save\",\"delete\":\"Delete\",\"edit\":\"Edit\",\"close\":\"Close\",\"back\":\"Back\",\"next\":\"Next\",\"previous\":\"Previous\",\"search\":\"Search\",\"filter\":\"Filter\",\"sort\":\"Sort\",\"export\":\"Export\",\"import\":\"Import\"},\"navigation\":{\"home\":\"Home\",\"about\":\"About\",\"features\":\"Features\",\"pricing\":\"Pricing\",\"contact\":\"Contact\",\"privacy\":\"Privacy\",\"terms\":\"Terms\",\"dashboard\":\"Dashboard\",\"tasks\":\"Tasks\",\"budget\":\"Budget\",\"shopping\":\"Shopping\",\"chat\":\"AI Chat\",\"recipes\":\"Recipes\",\"login\":\"Sign In\",\"signup\":\"Sign Up\",\"logout\":\"Sign Out\"},\"hero\":{\"badge\":\"AI-Powered Life Management\",\"title\":\"Manage Your Life\",\"subtitle\":\"The all-in-one platform to organize your tasks, track your budget, manage shopping lists, and get AI-powered assistance for better life management.\",\"cta_primary\":\"Start Free Today\",\"cta_secondary\":\"Watch Demo\",\"trust_users\":\"2.5M+ Users\",\"trust_rating\":\"4.9/5 Rating\",\"trust_security\":\"Enterprise Security\",\"scroll_indicator\":\"Scroll to explore\"},\"features\":{\"title\":\"Everything You Need in One Place\",\"subtitle\":\"Streamline your daily life with our comprehensive suite of management tools.\",\"task_management\":{\"title\":\"Smart Task Management\",\"description\":\"AI-powered task organization with drag-and-drop, priorities, and intelligent scheduling.\",\"stats\":\"50K+ daily\"},\"budget_tracking\":{\"title\":\"Advanced Budget Tracking\",\"description\":\"Real-time expense monitoring with predictive analytics and automated categorization.\",\"stats\":\"98% accuracy\"},\"shopping_lists\":{\"title\":\"Intelligent Shopping Lists\",\"description\":\"Smart shopping lists with price tracking, store optimization, and family sharing.\",\"stats\":\"Save 20%\"},\"ai_chat\":{\"title\":\"AI Life Assistant\",\"description\":\"Personalized recommendations and insights powered by advanced machine learning.\",\"stats\":\"24/7 available\"},\"recipes\":{\"title\":\"Recipe Management\",\"description\":\"Auto-import recipes, nutritional analysis, and meal planning with dietary preferences.\",\"stats\":\"10K+ recipes\"},\"security\":{\"title\":\"Security & Privacy\",\"description\":\"End-to-end encryption with real-time sync across all your devices worldwide.\",\"stats\":\"Bank-grade\"}},\"testimonials\":{\"title\":\"Loved by Users Worldwide\",\"subtitle\":\"See what our community has to say about transforming their daily lives.\"},\"stats\":{\"title\":\"Trusted Globally\",\"subtitle\":\"Join millions of users who have transformed their productivity and life management.\",\"users\":\"Active Users Worldwide\",\"countries\":\"Countries Supported\",\"tasks\":\"Tasks Completed Daily\",\"satisfaction\":\"Customer Satisfaction\",\"growing\":\"Growing daily\",\"global\":\"Global reach\",\"productivity\":\"Productivity boost\",\"happy\":\"Happy users\"},\"pricing\":{\"title\":\"Choose Your Plan\",\"subtitle\":\"Start free and upgrade as you grow. All plans include our core features.\",\"monthly\":\"Monthly\",\"yearly\":\"Yearly\",\"save\":\"Save 20%\",\"most_popular\":\"Most Popular\",\"free\":{\"name\":\"Free\",\"description\":\"Perfect for getting started\",\"cta\":\"Get Started Free\"},\"pro\":{\"name\":\"Pro\",\"description\":\"For power users and families\",\"cta\":\"Start Pro Trial\"},\"enterprise\":{\"name\":\"Enterprise\",\"description\":\"For organizations and teams\",\"cta\":\"Contact Sales\"},\"comparison\":\"Show Detailed Comparison\",\"guarantee\":\"30-Day Money-Back Guarantee\"},\"cta\":{\"title\":\"Ready to Take Control?\",\"subtitle\":\"Join thousands of users who have transformed their daily routines with LifeManager.\",\"button\":\"Get Started Free\"},\"footer\":{\"tagline\":\"Simplifying life management, one task at a time. Join the revolution of organized living.\",\"copyright\":\"© 2024 LifeManager. All rights reserved. Made with ❤️ for productivity enthusiasts worldwide.\"},\"contact\":{\"title\":\"We're Here to Help\",\"subtitle\":\"Have questions about LifeManager? Need help getting started? Our friendly support team is ready to assist you every step of the way.\",\"form\":{\"title\":\"Send us a Message\",\"name\":\"Name\",\"email\":\"Email\",\"company\":\"Company (Optional)\",\"subject\":\"Subject\",\"message\":\"Message\",\"type\":\"Inquiry Type\",\"send\":\"Send Message\",\"success\":\"Message Sent!\",\"success_message\":\"We'll get back to you within 24 hours.\"},\"methods\":{\"email\":{\"title\":\"Email Support\",\"description\":\"Get help via email\",\"response\":\"Within 24 hours\"},\"chat\":{\"title\":\"Live Chat\",\"description\":\"Chat with our team\",\"response\":\"Instant response\"},\"phone\":{\"title\":\"Phone Support\",\"description\":\"Enterprise customers\",\"response\":\"Business hours\"}}},\"about\":{\"title\":\"Simplifying Life, One Task at a Time\",\"subtitle\":\"Founded in 2019, LifeManager was born from a simple belief: everyone deserves tools that make their daily life easier, not more complicated.\",\"mission\":{\"title\":\"Our Mission\",\"description\":\"We believe that technology should enhance human potential, not overwhelm it. Our mission is to create intuitive, powerful tools that help people organize their lives, achieve their goals, and find better work-life balance.\"},\"values\":{\"title\":\"Our Values\",\"subtitle\":\"The principles that guide everything we do\"},\"team\":{\"title\":\"Meet Our Team\",\"subtitle\":\"The passionate people behind LifeManager\"},\"journey\":{\"title\":\"Our Journey\",\"subtitle\":\"Key milestones in our mission to simplify life management\"}}}"));}}),
"[project]/src/i18n/messages/es.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"common\":{\"loading\":\"Cargando...\",\"error\":\"Error\",\"success\":\"Éxito\",\"cancel\":\"Cancelar\",\"save\":\"Guardar\",\"delete\":\"Eliminar\",\"edit\":\"Editar\",\"close\":\"Cerrar\",\"back\":\"Atrás\",\"next\":\"Siguiente\",\"previous\":\"Anterior\",\"search\":\"Buscar\",\"filter\":\"Filtrar\",\"sort\":\"Ordenar\",\"export\":\"Exportar\",\"import\":\"Importar\"},\"navigation\":{\"home\":\"Inicio\",\"about\":\"Acerca de\",\"features\":\"Características\",\"pricing\":\"Precios\",\"contact\":\"Contacto\",\"privacy\":\"Privacidad\",\"terms\":\"Términos\",\"dashboard\":\"Panel\",\"tasks\":\"Tareas\",\"budget\":\"Presupuesto\",\"shopping\":\"Compras\",\"chat\":\"Chat IA\",\"recipes\":\"Recetas\",\"login\":\"Iniciar Sesión\",\"signup\":\"Registrarse\",\"logout\":\"Cerrar Sesión\"},\"hero\":{\"badge\":\"Gestión de Vida con IA\",\"title\":\"Gestiona Tu Vida\",\"subtitle\":\"La plataforma todo-en-uno para organizar tus tareas, rastrear tu presupuesto, gestionar listas de compras y obtener asistencia con IA para una mejor gestión de vida.\",\"cta_primary\":\"Comenzar Gratis Hoy\",\"cta_secondary\":\"Ver Demo\",\"trust_users\":\"2.5M+ Usuarios\",\"trust_rating\":\"4.9/5 Calificación\",\"trust_security\":\"Seguridad Empresarial\",\"scroll_indicator\":\"Desplázate para explorar\"},\"features\":{\"title\":\"Todo Lo Que Necesitas en Un Solo Lugar\",\"subtitle\":\"Optimiza tu vida diaria con nuestro conjunto completo de herramientas de gestión.\",\"task_management\":{\"title\":\"Gestión Inteligente de Tareas\",\"description\":\"Organización de tareas con IA, arrastrar y soltar, prioridades y programación inteligente.\",\"stats\":\"50K+ diarias\"},\"budget_tracking\":{\"title\":\"Seguimiento Avanzado de Presupuesto\",\"description\":\"Monitoreo de gastos en tiempo real con análisis predictivo y categorización automatizada.\",\"stats\":\"98% precisión\"},\"shopping_lists\":{\"title\":\"Listas de Compras Inteligentes\",\"description\":\"Listas de compras inteligentes con seguimiento de precios, optimización de tiendas y compartir familiar.\",\"stats\":\"Ahorra 20%\"},\"ai_chat\":{\"title\":\"Asistente de Vida IA\",\"description\":\"Recomendaciones personalizadas e insights impulsados por aprendizaje automático avanzado.\",\"stats\":\"24/7 disponible\"},\"recipes\":{\"title\":\"Gestión de Recetas\",\"description\":\"Importación automática de recetas, análisis nutricional y planificación de comidas con preferencias dietéticas.\",\"stats\":\"10K+ recetas\"},\"security\":{\"title\":\"Seguridad y Privacidad\",\"description\":\"Cifrado de extremo a extremo con sincronización en tiempo real en todos tus dispositivos mundialmente.\",\"stats\":\"Grado bancario\"}},\"testimonials\":{\"title\":\"Amado por Usuarios en Todo el Mundo\",\"subtitle\":\"Ve lo que nuestra comunidad dice sobre transformar sus vidas diarias.\"},\"stats\":{\"title\":\"Confiado Globalmente\",\"subtitle\":\"Únete a millones de usuarios que han transformado su productividad y gestión de vida.\",\"users\":\"Usuarios Activos Mundialmente\",\"countries\":\"Países Soportados\",\"tasks\":\"Tareas Completadas Diariamente\",\"satisfaction\":\"Satisfacción del Cliente\",\"growing\":\"Creciendo diariamente\",\"global\":\"Alcance global\",\"productivity\":\"Impulso de productividad\",\"happy\":\"Usuarios felices\"},\"pricing\":{\"title\":\"Elige Tu Plan\",\"subtitle\":\"Comienza gratis y actualiza mientras creces. Todos los planes incluyen nuestras características principales.\",\"monthly\":\"Mensual\",\"yearly\":\"Anual\",\"save\":\"Ahorra 20%\",\"most_popular\":\"Más Popular\",\"free\":{\"name\":\"Gratis\",\"description\":\"Perfecto para comenzar\",\"cta\":\"Comenzar Gratis\"},\"pro\":{\"name\":\"Pro\",\"description\":\"Para usuarios avanzados y familias\",\"cta\":\"Iniciar Prueba Pro\"},\"enterprise\":{\"name\":\"Empresarial\",\"description\":\"Para organizaciones y equipos\",\"cta\":\"Contactar Ventas\"},\"comparison\":\"Mostrar Comparación Detallada\",\"guarantee\":\"Garantía de Devolución de 30 Días\"},\"cta\":{\"title\":\"¿Listo para Tomar Control?\",\"subtitle\":\"Únete a miles de usuarios que han transformado sus rutinas diarias con LifeManager.\",\"button\":\"Comenzar Gratis\"},\"footer\":{\"tagline\":\"Simplificando la gestión de vida, una tarea a la vez. Únete a la revolución de la vida organizada.\",\"copyright\":\"© 2024 LifeManager. Todos los derechos reservados. Hecho con ❤️ para entusiastas de la productividad mundialmente.\"},\"contact\":{\"title\":\"Estamos Aquí para Ayudar\",\"subtitle\":\"¿Tienes preguntas sobre LifeManager? ¿Necesitas ayuda para comenzar? Nuestro amigable equipo de soporte está listo para asistirte en cada paso del camino.\",\"form\":{\"title\":\"Envíanos un Mensaje\",\"name\":\"Nombre\",\"email\":\"Email\",\"company\":\"Empresa (Opcional)\",\"subject\":\"Asunto\",\"message\":\"Mensaje\",\"type\":\"Tipo de Consulta\",\"send\":\"Enviar Mensaje\",\"success\":\"¡Mensaje Enviado!\",\"success_message\":\"Te responderemos dentro de 24 horas.\"},\"methods\":{\"email\":{\"title\":\"Soporte por Email\",\"description\":\"Obtén ayuda por email\",\"response\":\"Dentro de 24 horas\"},\"chat\":{\"title\":\"Chat en Vivo\",\"description\":\"Chatea con nuestro equipo\",\"response\":\"Respuesta instantánea\"},\"phone\":{\"title\":\"Soporte Telefónico\",\"description\":\"Clientes empresariales\",\"response\":\"Horario comercial\"}}},\"about\":{\"title\":\"Simplificando la Vida, Una Tarea a la Vez\",\"subtitle\":\"Fundado en 2019, LifeManager nació de una creencia simple: todos merecen herramientas que hagan su vida diaria más fácil, no más complicada.\",\"mission\":{\"title\":\"Nuestra Misión\",\"description\":\"Creemos que la tecnología debe potenciar el potencial humano, no abrumarlo. Nuestra misión es crear herramientas intuitivas y poderosas que ayuden a las personas a organizar sus vidas, lograr sus objetivos y encontrar un mejor equilibrio trabajo-vida.\"},\"values\":{\"title\":\"Nuestros Valores\",\"subtitle\":\"Los principios que guían todo lo que hacemos\"},\"team\":{\"title\":\"Conoce Nuestro Equipo\",\"subtitle\":\"Las personas apasionadas detrás de LifeManager\"},\"journey\":{\"title\":\"Nuestro Viaje\",\"subtitle\":\"Hitos clave en nuestra misión de simplificar la gestión de vida\"}}}"));}}),
"[project]/src/i18n/messages/fr.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"common\":{\"loading\":\"Chargement...\",\"error\":\"Erreur\",\"success\":\"Succès\",\"cancel\":\"Annuler\",\"save\":\"Enregistrer\",\"delete\":\"Supprimer\",\"edit\":\"Modifier\",\"close\":\"Fermer\",\"back\":\"Retour\",\"next\":\"Suivant\",\"previous\":\"Précédent\",\"search\":\"Rechercher\",\"filter\":\"Filtrer\",\"sort\":\"Trier\",\"export\":\"Exporter\",\"import\":\"Importer\"},\"navigation\":{\"home\":\"Accueil\",\"about\":\"À propos\",\"features\":\"Fonctionnalités\",\"pricing\":\"Tarifs\",\"contact\":\"Contact\",\"privacy\":\"Confidentialité\",\"terms\":\"Conditions\",\"dashboard\":\"Tableau de bord\",\"tasks\":\"Tâches\",\"budget\":\"Budget\",\"shopping\":\"Achats\",\"chat\":\"Chat IA\",\"recipes\":\"Recettes\",\"login\":\"Se connecter\",\"signup\":\"S'inscrire\",\"logout\":\"Se déconnecter\"},\"hero\":{\"badge\":\"Gestion de Vie Alimentée par l'IA\",\"title\":\"Gérez Votre Vie\",\"subtitle\":\"La plateforme tout-en-un pour organiser vos tâches, suivre votre budget, gérer vos listes de courses et obtenir une assistance IA pour une meilleure gestion de vie.\",\"cta_primary\":\"Commencer Gratuitement\",\"cta_secondary\":\"Voir la Démo\",\"trust_users\":\"2.5M+ Utilisateurs\",\"trust_rating\":\"4.9/5 Évaluation\",\"trust_security\":\"Sécurité Entreprise\",\"scroll_indicator\":\"Faites défiler pour explorer\"},\"features\":{\"title\":\"Tout Ce Dont Vous Avez Besoin en Un Seul Endroit\",\"subtitle\":\"Rationalisez votre vie quotidienne avec notre suite complète d'outils de gestion.\",\"task_management\":{\"title\":\"Gestion Intelligente des Tâches\",\"description\":\"Organisation des tâches alimentée par l'IA avec glisser-déposer, priorités et planification intelligente.\",\"stats\":\"50K+ quotidiennes\"},\"budget_tracking\":{\"title\":\"Suivi Avancé du Budget\",\"description\":\"Surveillance des dépenses en temps réel avec analyses prédictives et catégorisation automatisée.\",\"stats\":\"98% précision\"},\"shopping_lists\":{\"title\":\"Listes de Courses Intelligentes\",\"description\":\"Listes de courses intelligentes avec suivi des prix, optimisation des magasins et partage familial.\",\"stats\":\"Économisez 20%\"},\"ai_chat\":{\"title\":\"Assistant de Vie IA\",\"description\":\"Recommandations personnalisées et insights alimentés par l'apprentissage automatique avancé.\",\"stats\":\"24/7 disponible\"},\"recipes\":{\"title\":\"Gestion des Recettes\",\"description\":\"Import automatique de recettes, analyse nutritionnelle et planification de repas avec préférences diététiques.\",\"stats\":\"10K+ recettes\"},\"security\":{\"title\":\"Sécurité et Confidentialité\",\"description\":\"Chiffrement de bout en bout avec synchronisation en temps réel sur tous vos appareils dans le monde.\",\"stats\":\"Niveau bancaire\"}},\"testimonials\":{\"title\":\"Aimé par les Utilisateurs du Monde Entier\",\"subtitle\":\"Découvrez ce que notre communauté dit sur la transformation de leur vie quotidienne.\"},\"stats\":{\"title\":\"Fait Confiance Mondialement\",\"subtitle\":\"Rejoignez des millions d'utilisateurs qui ont transformé leur productivité et gestion de vie.\",\"users\":\"Utilisateurs Actifs Mondialement\",\"countries\":\"Pays Supportés\",\"tasks\":\"Tâches Complétées Quotidiennement\",\"satisfaction\":\"Satisfaction Client\",\"growing\":\"Croissance quotidienne\",\"global\":\"Portée mondiale\",\"productivity\":\"Boost de productivité\",\"happy\":\"Utilisateurs heureux\"},\"pricing\":{\"title\":\"Choisissez Votre Plan\",\"subtitle\":\"Commencez gratuitement et évoluez en grandissant. Tous les plans incluent nos fonctionnalités principales.\",\"monthly\":\"Mensuel\",\"yearly\":\"Annuel\",\"save\":\"Économisez 20%\",\"most_popular\":\"Le Plus Populaire\",\"free\":{\"name\":\"Gratuit\",\"description\":\"Parfait pour commencer\",\"cta\":\"Commencer Gratuitement\"},\"pro\":{\"name\":\"Pro\",\"description\":\"Pour les utilisateurs avancés et familles\",\"cta\":\"Démarrer l'Essai Pro\"},\"enterprise\":{\"name\":\"Entreprise\",\"description\":\"Pour les organisations et équipes\",\"cta\":\"Contacter les Ventes\"},\"comparison\":\"Afficher la Comparaison Détaillée\",\"guarantee\":\"Garantie de Remboursement de 30 Jours\"},\"cta\":{\"title\":\"Prêt à Prendre le Contrôle ?\",\"subtitle\":\"Rejoignez des milliers d'utilisateurs qui ont transformé leurs routines quotidiennes avec LifeManager.\",\"button\":\"Commencer Gratuitement\"},\"footer\":{\"tagline\":\"Simplifier la gestion de vie, une tâche à la fois. Rejoignez la révolution de la vie organisée.\",\"copyright\":\"© 2024 LifeManager. Tous droits réservés. Fait avec ❤️ pour les passionnés de productivité dans le monde.\"},\"contact\":{\"title\":\"Nous Sommes Là pour Aider\",\"subtitle\":\"Avez-vous des questions sur LifeManager ? Besoin d'aide pour commencer ? Notre équipe de support amicale est prête à vous assister à chaque étape.\",\"form\":{\"title\":\"Envoyez-nous un Message\",\"name\":\"Nom\",\"email\":\"Email\",\"company\":\"Entreprise (Optionnel)\",\"subject\":\"Sujet\",\"message\":\"Message\",\"type\":\"Type de Demande\",\"send\":\"Envoyer le Message\",\"success\":\"Message Envoyé !\",\"success_message\":\"Nous vous répondrons dans les 24 heures.\"},\"methods\":{\"email\":{\"title\":\"Support Email\",\"description\":\"Obtenez de l'aide par email\",\"response\":\"Dans les 24 heures\"},\"chat\":{\"title\":\"Chat en Direct\",\"description\":\"Chattez avec notre équipe\",\"response\":\"Réponse instantanée\"},\"phone\":{\"title\":\"Support Téléphonique\",\"description\":\"Clients entreprise\",\"response\":\"Heures d'ouverture\"}}},\"about\":{\"title\":\"Simplifier la Vie, Une Tâche à la Fois\",\"subtitle\":\"Fondé en 2019, LifeManager est né d'une croyance simple : tout le monde mérite des outils qui rendent sa vie quotidienne plus facile, pas plus compliquée.\",\"mission\":{\"title\":\"Notre Mission\",\"description\":\"Nous croyons que la technologie devrait améliorer le potentiel humain, pas l'accabler. Notre mission est de créer des outils intuitifs et puissants qui aident les gens à organiser leur vie, atteindre leurs objectifs et trouver un meilleur équilibre travail-vie.\"},\"values\":{\"title\":\"Nos Valeurs\",\"subtitle\":\"Les principes qui guident tout ce que nous faisons\"},\"team\":{\"title\":\"Rencontrez Notre Équipe\",\"subtitle\":\"Les personnes passionnées derrière LifeManager\"},\"journey\":{\"title\":\"Notre Parcours\",\"subtitle\":\"Étapes clés de notre mission pour simplifier la gestion de vie\"}}}"));}}),
"[project]/src/i18n/messages/ja.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"common\":{\"loading\":\"読み込み中...\",\"error\":\"エラー\",\"success\":\"成功\",\"cancel\":\"キャンセル\",\"save\":\"保存\",\"delete\":\"削除\",\"edit\":\"編集\",\"close\":\"閉じる\",\"back\":\"戻る\",\"next\":\"次へ\",\"previous\":\"前へ\",\"search\":\"検索\",\"filter\":\"フィルター\",\"sort\":\"並び替え\",\"export\":\"エクスポート\",\"import\":\"インポート\"},\"navigation\":{\"home\":\"ホーム\",\"about\":\"会社概要\",\"features\":\"機能\",\"pricing\":\"料金\",\"contact\":\"お問い合わせ\",\"privacy\":\"プライバシー\",\"terms\":\"利用規約\",\"dashboard\":\"ダッシュボード\",\"tasks\":\"タスク\",\"budget\":\"予算\",\"shopping\":\"ショッピング\",\"chat\":\"AIチャット\",\"recipes\":\"レシピ\",\"login\":\"ログイン\",\"signup\":\"サインアップ\",\"logout\":\"ログアウト\"},\"hero\":{\"badge\":\"AI搭載ライフマネジメント\",\"title\":\"あなたの人生を管理\",\"subtitle\":\"タスクの整理、予算の追跡、買い物リストの管理、そしてより良いライフマネジメントのためのAI支援を提供するオールインワンプラットフォーム。\",\"cta_primary\":\"今すぐ無料で始める\",\"cta_secondary\":\"デモを見る\",\"trust_users\":\"250万人以上のユーザー\",\"trust_rating\":\"4.9/5の評価\",\"trust_security\":\"エンタープライズセキュリティ\",\"scroll_indicator\":\"スクロールして探索\"},\"features\":{\"title\":\"必要なものすべてが一箇所に\",\"subtitle\":\"包括的な管理ツールスイートで日常生活を合理化。\",\"task_management\":{\"title\":\"スマートタスク管理\",\"description\":\"ドラッグ&ドロップ、優先度、インテリジェントスケジューリングを備えたAI搭載タスク整理。\",\"stats\":\"毎日5万件以上\"},\"budget_tracking\":{\"title\":\"高度な予算追跡\",\"description\":\"予測分析と自動分類によるリアルタイム支出監視。\",\"stats\":\"98%の精度\"},\"shopping_lists\":{\"title\":\"インテリジェント買い物リスト\",\"description\":\"価格追跡、店舗最適化、家族共有機能付きのスマート買い物リスト。\",\"stats\":\"20%節約\"},\"ai_chat\":{\"title\":\"AIライフアシスタント\",\"description\":\"高度な機械学習によるパーソナライズされた推奨事項と洞察。\",\"stats\":\"24時間365日利用可能\"},\"recipes\":{\"title\":\"レシピ管理\",\"description\":\"レシピの自動インポート、栄養分析、食事の好みに基づく食事計画。\",\"stats\":\"1万件以上のレシピ\"},\"security\":{\"title\":\"セキュリティとプライバシー\",\"description\":\"世界中のすべてのデバイスでリアルタイム同期を備えたエンドツーエンド暗号化。\",\"stats\":\"銀行レベル\"}},\"testimonials\":{\"title\":\"世界中のユーザーに愛されています\",\"subtitle\":\"日常生活の変革について、コミュニティの声をご覧ください。\"},\"stats\":{\"title\":\"世界的に信頼されています\",\"subtitle\":\"生産性とライフマネジメントを変革した数百万人のユーザーに参加してください。\",\"users\":\"世界中のアクティブユーザー\",\"countries\":\"サポート国数\",\"tasks\":\"毎日完了するタスク\",\"satisfaction\":\"顧客満足度\",\"growing\":\"毎日成長\",\"global\":\"グローバルリーチ\",\"productivity\":\"生産性向上\",\"happy\":\"満足したユーザー\"},\"pricing\":{\"title\":\"プランを選択\",\"subtitle\":\"無料で始めて、成長に合わせてアップグレード。すべてのプランにコア機能が含まれています。\",\"monthly\":\"月額\",\"yearly\":\"年額\",\"save\":\"20%節約\",\"most_popular\":\"最も人気\",\"free\":{\"name\":\"無料\",\"description\":\"始めるのに最適\",\"cta\":\"無料で始める\"},\"pro\":{\"name\":\"プロ\",\"description\":\"パワーユーザーと家族向け\",\"cta\":\"プロトライアル開始\"},\"enterprise\":{\"name\":\"エンタープライズ\",\"description\":\"組織とチーム向け\",\"cta\":\"営業に連絡\"},\"comparison\":\"詳細比較を表示\",\"guarantee\":\"30日間返金保証\"},\"cta\":{\"title\":\"コントロールを取る準備はできましたか？\",\"subtitle\":\"LifeManagerで日常のルーチンを変革した何千人ものユーザーに参加してください。\",\"button\":\"無料で始める\"},\"footer\":{\"tagline\":\"一度に一つのタスクでライフマネジメントを簡素化。整理された生活の革命に参加してください。\",\"copyright\":\"© 2024 LifeManager. 全著作権所有。世界中の生産性愛好家のために❤️で作られました。\"},\"contact\":{\"title\":\"お手伝いします\",\"subtitle\":\"LifeManagerについて質問がありますか？始めるのにヘルプが必要ですか？フレンドリーなサポートチームがあらゆるステップでお手伝いします。\",\"form\":{\"title\":\"メッセージを送信\",\"name\":\"名前\",\"email\":\"メール\",\"company\":\"会社（任意）\",\"subject\":\"件名\",\"message\":\"メッセージ\",\"type\":\"お問い合わせタイプ\",\"send\":\"メッセージを送信\",\"success\":\"メッセージが送信されました！\",\"success_message\":\"24時間以内にご返信いたします。\"},\"methods\":{\"email\":{\"title\":\"メールサポート\",\"description\":\"メールでヘルプを受ける\",\"response\":\"24時間以内\"},\"chat\":{\"title\":\"ライブチャット\",\"description\":\"チームとチャット\",\"response\":\"即座に対応\"},\"phone\":{\"title\":\"電話サポート\",\"description\":\"エンタープライズ顧客\",\"response\":\"営業時間内\"}}},\"about\":{\"title\":\"一度に一つのタスクで人生を簡素化\",\"subtitle\":\"2019年に設立されたLifeManagerは、シンプルな信念から生まれました：誰もが日常生活をより簡単にするツールを持つ権利があり、複雑にするものではありません。\",\"mission\":{\"title\":\"私たちの使命\",\"description\":\"テクノロジーは人間の可能性を高めるべきであり、圧倒するものではないと信じています。私たちの使命は、人々が生活を整理し、目標を達成し、より良いワークライフバランスを見つけるのに役立つ直感的で強力なツールを作ることです。\"},\"values\":{\"title\":\"私たちの価値観\",\"subtitle\":\"私たちが行うすべてを導く原則\"},\"team\":{\"title\":\"チームに会う\",\"subtitle\":\"LifeManagerの背後にいる情熱的な人々\"},\"journey\":{\"title\":\"私たちの旅\",\"subtitle\":\"ライフマネジメントを簡素化する使命における重要なマイルストーン\"}}}"));}}),
"[project]/src/i18n/messages/zh.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"common\":{\"loading\":\"加载中...\",\"error\":\"错误\",\"success\":\"成功\",\"cancel\":\"取消\",\"save\":\"保存\",\"delete\":\"删除\",\"edit\":\"编辑\",\"close\":\"关闭\",\"back\":\"返回\",\"next\":\"下一步\",\"previous\":\"上一步\",\"search\":\"搜索\",\"filter\":\"筛选\",\"sort\":\"排序\",\"export\":\"导出\",\"import\":\"导入\"},\"navigation\":{\"home\":\"首页\",\"about\":\"关于我们\",\"features\":\"功能\",\"pricing\":\"定价\",\"contact\":\"联系我们\",\"privacy\":\"隐私\",\"terms\":\"条款\",\"dashboard\":\"仪表板\",\"tasks\":\"任务\",\"budget\":\"预算\",\"shopping\":\"购物\",\"chat\":\"AI聊天\",\"recipes\":\"食谱\",\"login\":\"登录\",\"signup\":\"注册\",\"logout\":\"退出\"},\"hero\":{\"badge\":\"AI驱动的生活管理\",\"title\":\"管理您的生活\",\"subtitle\":\"一体化平台，用于组织任务、跟踪预算、管理购物清单，并获得AI驱动的协助，实现更好的生活管理。\",\"cta_primary\":\"今天免费开始\",\"cta_secondary\":\"观看演示\",\"trust_users\":\"250万+用户\",\"trust_rating\":\"4.9/5评分\",\"trust_security\":\"企业级安全\",\"scroll_indicator\":\"滚动探索\"},\"features\":{\"title\":\"您需要的一切都在一个地方\",\"subtitle\":\"通过我们全面的管理工具套件简化您的日常生活。\",\"task_management\":{\"title\":\"智能任务管理\",\"description\":\"AI驱动的任务组织，具有拖放、优先级和智能调度功能。\",\"stats\":\"每天5万+\"},\"budget_tracking\":{\"title\":\"高级预算跟踪\",\"description\":\"实时支出监控，具有预测分析和自动分类功能。\",\"stats\":\"98%准确率\"},\"shopping_lists\":{\"title\":\"智能购物清单\",\"description\":\"智能购物清单，具有价格跟踪、商店优化和家庭共享功能。\",\"stats\":\"节省20%\"},\"ai_chat\":{\"title\":\"AI生活助手\",\"description\":\"由先进机器学习驱动的个性化推荐和洞察。\",\"stats\":\"24/7可用\"},\"recipes\":{\"title\":\"食谱管理\",\"description\":\"自动导入食谱、营养分析和基于饮食偏好的膳食计划。\",\"stats\":\"1万+食谱\"},\"security\":{\"title\":\"安全与隐私\",\"description\":\"端到端加密，在全球所有设备上实时同步。\",\"stats\":\"银行级\"}},\"testimonials\":{\"title\":\"受到全球用户喜爱\",\"subtitle\":\"看看我们的社区对改变日常生活的看法。\"},\"stats\":{\"title\":\"全球信赖\",\"subtitle\":\"加入数百万已经改变生产力和生活管理的用户。\",\"users\":\"全球活跃用户\",\"countries\":\"支持的国家\",\"tasks\":\"每日完成的任务\",\"satisfaction\":\"客户满意度\",\"growing\":\"每日增长\",\"global\":\"全球覆盖\",\"productivity\":\"生产力提升\",\"happy\":\"满意用户\"},\"pricing\":{\"title\":\"选择您的计划\",\"subtitle\":\"免费开始，随着成长而升级。所有计划都包含我们的核心功能。\",\"monthly\":\"月付\",\"yearly\":\"年付\",\"save\":\"节省20%\",\"most_popular\":\"最受欢迎\",\"free\":{\"name\":\"免费\",\"description\":\"开始的完美选择\",\"cta\":\"免费开始\"},\"pro\":{\"name\":\"专业版\",\"description\":\"适合高级用户和家庭\",\"cta\":\"开始专业版试用\"},\"enterprise\":{\"name\":\"企业版\",\"description\":\"适合组织和团队\",\"cta\":\"联系销售\"},\"comparison\":\"显示详细比较\",\"guarantee\":\"30天退款保证\"},\"cta\":{\"title\":\"准备好掌控一切了吗？\",\"subtitle\":\"加入成千上万使用LifeManager改变日常例行公事的用户。\",\"button\":\"免费开始\"},\"footer\":{\"tagline\":\"简化生活管理，一次一个任务。加入有组织生活的革命。\",\"copyright\":\"© 2024 LifeManager. 保留所有权利。为全球生产力爱好者用❤️制作。\"},\"contact\":{\"title\":\"我们在这里帮助您\",\"subtitle\":\"对LifeManager有疑问吗？需要帮助开始吗？我们友好的支持团队准备在每一步为您提供帮助。\",\"form\":{\"title\":\"给我们发送消息\",\"name\":\"姓名\",\"email\":\"邮箱\",\"company\":\"公司（可选）\",\"subject\":\"主题\",\"message\":\"消息\",\"type\":\"咨询类型\",\"send\":\"发送消息\",\"success\":\"消息已发送！\",\"success_message\":\"我们将在24小时内回复您。\"},\"methods\":{\"email\":{\"title\":\"邮件支持\",\"description\":\"通过邮件获得帮助\",\"response\":\"24小时内\"},\"chat\":{\"title\":\"在线聊天\",\"description\":\"与我们的团队聊天\",\"response\":\"即时响应\"},\"phone\":{\"title\":\"电话支持\",\"description\":\"企业客户\",\"response\":\"工作时间\"}}},\"about\":{\"title\":\"简化生活，一次一个任务\",\"subtitle\":\"成立于2019年，LifeManager诞生于一个简单的信念：每个人都应该拥有让日常生活更轻松而不是更复杂的工具。\",\"mission\":{\"title\":\"我们的使命\",\"description\":\"我们相信技术应该增强人类潜力，而不是压倒它。我们的使命是创造直观、强大的工具，帮助人们组织生活、实现目标并找到更好的工作生活平衡。\"},\"values\":{\"title\":\"我们的价值观\",\"subtitle\":\"指导我们所做一切的原则\"},\"team\":{\"title\":\"认识我们的团队\",\"subtitle\":\"LifeManager背后充满激情的人们\"},\"journey\":{\"title\":\"我们的旅程\",\"subtitle\":\"简化生活管理使命中的关键里程碑\"}}}"));}}),
"[project]/src/i18n/request.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getRequestConfig$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__default__as__getRequestConfig$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js [middleware-edge] (ecmascript) <export default as getRequestConfig>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/i18n/config.ts [middleware-edge] (ecmascript)");
;
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getRequestConfig$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__default__as__getRequestConfig$3e$__["getRequestConfig"])(async ({ locale })=>{
    // Validate that the incoming `locale` parameter is valid
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["locales"].includes(locale)) {
        locale = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["defaultLocale"];
    }
    return {
        messages: (await __turbopack_context__.f({
            "./messages/de.json": {
                id: ()=>"[project]/src/i18n/messages/de.json (json)",
                module: ()=>Promise.resolve().then(()=>__turbopack_context__.i("[project]/src/i18n/messages/de.json (json)"))
            },
            "./messages/en.json": {
                id: ()=>"[project]/src/i18n/messages/en.json (json)",
                module: ()=>Promise.resolve().then(()=>__turbopack_context__.i("[project]/src/i18n/messages/en.json (json)"))
            },
            "./messages/es.json": {
                id: ()=>"[project]/src/i18n/messages/es.json (json)",
                module: ()=>Promise.resolve().then(()=>__turbopack_context__.i("[project]/src/i18n/messages/es.json (json)"))
            },
            "./messages/fr.json": {
                id: ()=>"[project]/src/i18n/messages/fr.json (json)",
                module: ()=>Promise.resolve().then(()=>__turbopack_context__.i("[project]/src/i18n/messages/fr.json (json)"))
            },
            "./messages/ja.json": {
                id: ()=>"[project]/src/i18n/messages/ja.json (json)",
                module: ()=>Promise.resolve().then(()=>__turbopack_context__.i("[project]/src/i18n/messages/ja.json (json)"))
            },
            "./messages/zh.json": {
                id: ()=>"[project]/src/i18n/messages/zh.json (json)",
                module: ()=>Promise.resolve().then(()=>__turbopack_context__.i("[project]/src/i18n/messages/zh.json (json)"))
            }
        }).import(`./messages/${locale}.json`)).default,
        timeZone: 'UTC',
        now: new Date(),
        formats: {
            dateTime: {
                short: {
                    day: 'numeric',
                    month: 'short',
                    year: 'numeric'
                },
                long: {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric',
                    hour: 'numeric',
                    minute: 'numeric'
                }
            },
            number: {
                currency: {
                    style: 'currency',
                    currency: 'USD'
                },
                percent: {
                    style: 'percent',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 2
                }
            }
        }
    };
});
}}),
"[project]/src/i18n/config.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Link": (()=>Link),
    "defaultLocale": (()=>defaultLocale),
    "localeFlags": (()=>localeFlags),
    "localeNames": (()=>localeNames),
    "locales": (()=>locales),
    "redirect": (()=>redirect),
    "usePathname": (()=>usePathname),
    "useRouter": (()=>useRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$navigation$2e$react$2d$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/navigation.react-server.js [middleware-edge] (ecmascript) <exports>");
;
const locales = [
    'en',
    'es',
    'fr',
    'de',
    'ja',
    'zh'
];
const defaultLocale = 'en';
const localeNames = {
    en: 'English',
    es: 'Español',
    fr: 'Français',
    de: 'Deutsch',
    ja: '日本語',
    zh: '中文'
};
const localeFlags = {
    en: '🇺🇸',
    es: '🇪🇸',
    fr: '🇫🇷',
    de: '🇩🇪',
    ja: '🇯🇵',
    zh: '🇨🇳'
};
const { Link, redirect, usePathname, useRouter } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$navigation$2e$react$2d$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$exports$3e$__["createSharedPathnamesNavigation"])({
    locales,
    pathnames: {
        '/': '/',
        '/about': {
            en: '/about',
            es: '/acerca-de',
            fr: '/a-propos',
            de: '/uber-uns',
            ja: '/about',
            zh: '/about'
        },
        '/features': {
            en: '/features',
            es: '/caracteristicas',
            fr: '/fonctionnalites',
            de: '/funktionen',
            ja: '/features',
            zh: '/features'
        },
        '/pricing': {
            en: '/pricing',
            es: '/precios',
            fr: '/tarifs',
            de: '/preise',
            ja: '/pricing',
            zh: '/pricing'
        },
        '/contact': {
            en: '/contact',
            es: '/contacto',
            fr: '/contact',
            de: '/kontakt',
            ja: '/contact',
            zh: '/contact'
        },
        '/privacy': {
            en: '/privacy',
            es: '/privacidad',
            fr: '/confidentialite',
            de: '/datenschutz',
            ja: '/privacy',
            zh: '/privacy'
        },
        '/terms': {
            en: '/terms',
            es: '/terminos',
            fr: '/conditions',
            de: '/bedingungen',
            ja: '/terms',
            zh: '/terms'
        }
    }
});
}}),
"[project]/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$middleware$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/middleware.ts [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$middleware$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/middleware/middleware.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/i18n/config.ts [middleware-edge] (ecmascript)");
;
;
;
const intlMiddleware = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$middleware$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])({
    locales: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["locales"],
    defaultLocale: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["defaultLocale"],
    localePrefix: 'as-needed'
});
async function middleware(request) {
    // Handle internationalization first
    const intlResponse = intlMiddleware(request);
    // Then handle Supabase session
    const supabaseResponse = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$middleware$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["updateSession"])(request);
    // Return the intl response if it exists (redirect), otherwise supabase response
    return intlResponse || supabaseResponse;
}
const config = {
    matcher: [
        /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */ '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__5c75ab5e._.js.map