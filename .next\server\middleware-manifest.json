{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Lzxq+LF28sVcjYZHZyFkzVYZUvwisl0bINLEPMHycW8=", "__NEXT_PREVIEW_MODE_ID": "06a2125b0dec70ddbffe29c68cc47897", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4807cc1fbe3cdfba0e220a9f842c32e4f1312adb7939db2305b6fd85133ea0e0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5ae2f11a13947c9ae3391aee6adc4a81d48cd239de27e1f6ada99a83a1234c8d"}}}, "sortedMiddleware": ["/"], "functions": {}}