{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_256bb82d._.js", "server/edge/chunks/node_modules_@supabase_auth-js_dist_module_17bbb6b5._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_dfe58992._.js", "server/edge/chunks/[root-of-the-server]__5c75ab5e._.js", "server/edge/chunks/edge-wrapper_fcc83bbc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Lzxq+LF28sVcjYZHZyFkzVYZUvwisl0bINLEPMHycW8=", "__NEXT_PREVIEW_MODE_ID": "7dc12ee4ab70923fe001689658786ad5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a5626e5ccf75a26f85f1d0c5e21c6767785e553040020ea5a9829859dcc13dd3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "af61d1749fe1de13d885221ea20f974031dc27e5eccd8a95b9863472385b30d7"}}}, "sortedMiddleware": ["/"], "functions": {}}