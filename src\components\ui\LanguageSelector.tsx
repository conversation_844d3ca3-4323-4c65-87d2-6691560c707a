'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { GlobeIcon, ChevronDownIcon, CheckIcon } from 'lucide-react'
import { useLocale } from 'next-intl'
import { useRouter, usePathname } from 'next/navigation'
import { locales, localeNames, localeFlags, type Locale } from '@/i18n/config'

interface LanguageSelectorProps {
  variant?: 'default' | 'compact'
  className?: string
}

export default function LanguageSelector({ 
  variant = 'default', 
  className = '' 
}: LanguageSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const locale = useLocale() as Locale
  const router = useRouter()
  const pathname = usePathname()
  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleLanguageChange = (newLocale: Locale) => {
    setIsOpen(false)
    
    // Create new path with the new locale
    const segments = pathname.split('/')
    segments[1] = newLocale
    const newPath = segments.join('/')
    
    router.push(newPath)
  }

  const currentLanguage = {
    code: locale,
    name: localeNames[locale],
    flag: localeFlags[locale]
  }

  if (variant === 'compact') {
    return (
      <div className={`relative ${className}`} ref={dropdownRef}>
        <motion.button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <span className="text-lg">{currentLanguage.flag}</span>
          <span className="hidden sm:inline">{currentLanguage.code.toUpperCase()}</span>
          <ChevronDownIcon className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </motion.button>

        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50"
            >
              {locales.map((localeCode) => (
                <motion.button
                  key={localeCode}
                  onClick={() => handleLanguageChange(localeCode)}
                  className="w-full flex items-center justify-between px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                  whileHover={{ backgroundColor: '#f3f4f6' }}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">{localeFlags[localeCode]}</span>
                    <span>{localeNames[localeCode]}</span>
                  </div>
                  {locale === localeCode && (
                    <CheckIcon className="h-4 w-4 text-blue-600" />
                  )}
                </motion.button>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    )
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-3 px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <GlobeIcon className="h-5 w-5 text-gray-600" />
        <div className="flex items-center space-x-2">
          <span className="text-xl">{currentLanguage.flag}</span>
          <span className="font-medium text-gray-900">{currentLanguage.name}</span>
        </div>
        <ChevronDownIcon className={`h-4 w-4 text-gray-600 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-xl border border-gray-200 py-2 z-50"
          >
            <div className="px-4 py-2 border-b border-gray-100">
              <h3 className="text-sm font-semibold text-gray-900">Choose Language</h3>
              <p className="text-xs text-gray-500">Select your preferred language</p>
            </div>
            
            <div className="py-1">
              {locales.map((localeCode) => (
                <motion.button
                  key={localeCode}
                  onClick={() => handleLanguageChange(localeCode)}
                  className="w-full flex items-center justify-between px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                  whileHover={{ backgroundColor: '#f9fafb' }}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{localeFlags[localeCode]}</span>
                    <div className="text-left">
                      <div className="font-medium">{localeNames[localeCode]}</div>
                      <div className="text-xs text-gray-500 uppercase">{localeCode}</div>
                    </div>
                  </div>
                  {locale === localeCode && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center"
                    >
                      <CheckIcon className="h-4 w-4 text-blue-600" />
                    </motion.div>
                  )}
                </motion.button>
              ))}
            </div>
            
            <div className="px-4 py-2 border-t border-gray-100">
              <p className="text-xs text-gray-500">
                More languages coming soon!
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Compact version for mobile/header use
export function CompactLanguageSelector(props: Omit<LanguageSelectorProps, 'variant'>) {
  return <LanguageSelector {...props} variant="compact" />
}
