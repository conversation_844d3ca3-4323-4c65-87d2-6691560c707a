{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Badge.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { cn } from '@/lib/utils'\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-all duration-200',\n  {\n    variants: {\n      variant: {\n        default: 'bg-blue-100 text-blue-800 hover:bg-blue-200',\n        secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200',\n        destructive: 'bg-red-100 text-red-800 hover:bg-red-200',\n        success: 'bg-green-100 text-green-800 hover:bg-green-200',\n        warning: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',\n        outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50',\n        purple: 'bg-purple-100 text-purple-800 hover:bg-purple-200',\n        indigo: 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200',\n        pink: 'bg-pink-100 text-pink-800 hover:bg-pink-200',\n        orange: 'bg-orange-100 text-orange-800 hover:bg-orange-200',\n      },\n      size: {\n        default: 'px-2.5 py-0.5 text-xs',\n        sm: 'px-2 py-0.5 text-xs',\n        lg: 'px-3 py-1 text-sm',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nconst Badge = forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant, size, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(badgeVariants({ variant, size }), className)}\n        {...props}\n      />\n    )\n  }\n)\nBadge.displayName = 'Badge'\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,uGACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,QAAQ;QACV;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IACvC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAC/C,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Card.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nconst Card = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border border-gray-200 bg-white text-gray-950 shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCard.displayName = 'Card'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex flex-col space-y-1.5 p-6', className)} {...props} />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('text-lg font-semibold leading-none tracking-tight text-gray-900', className)}\n      {...props}\n    />\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p ref={ref} className={cn('text-sm text-gray-600', className)} {...props} />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex items-center p-6 pt-0', className)} {...props} />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACpB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAGvF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAE,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QAAa,GAAG,KAAK;;;;;;AAG7E,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAGlE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAGpF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/terms/page.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport Link from 'next/link'\nimport { FileTextIcon, ScaleIcon, ShieldIcon, AlertTriangleIcon } from 'lucide-react'\nimport { Badge } from '@/components/ui/Badge'\nimport { Card } from '@/components/ui/Card'\n\nconst keyTerms = [\n  {\n    icon: ScaleIcon,\n    title: 'Fair Use',\n    description: 'Use our services responsibly and in accordance with applicable laws.'\n  },\n  {\n    icon: ShieldIcon,\n    title: 'Account Security',\n    description: 'You are responsible for maintaining the security of your account credentials.'\n  },\n  {\n    icon: AlertTriangleIcon,\n    title: 'Service Availability',\n    description: 'We strive for 99.9% uptime but cannot guarantee uninterrupted service.'\n  },\n  {\n    icon: FileTextIcon,\n    title: 'Content Ownership',\n    description: 'You retain ownership of your content while granting us necessary licenses to provide our services.'\n  }\n]\n\nexport default function TermsPage() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">LifeManager</h1>\n            </Link>\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/\" className=\"text-gray-600 hover:text-gray-900 font-medium\">\n                Home\n              </Link>\n              <Link href=\"/about\" className=\"text-gray-600 hover:text-gray-900 font-medium\">\n                About\n              </Link>\n              <Link href=\"/contact\" className=\"text-gray-600 hover:text-gray-900 font-medium\">\n                Contact\n              </Link>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"py-20\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <Badge variant=\"orange\" size=\"lg\" className=\"mb-6\">\n              <FileTextIcon className=\"h-4 w-4 mr-2\" />\n              Terms of Service\n            </Badge>\n            <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Terms of\n              <span className=\"text-blue-600\"> Service</span>\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 leading-relaxed\">\n              These terms govern your use of LifeManager. By using our services, \n              you agree to be bound by these terms.\n            </p>\n            <p className=\"text-sm text-gray-500\">\n              Last updated: December 15, 2024\n            </p>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Key Terms */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            className=\"text-center mb-16\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Key Terms Summary\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              The essential points you should know about using our services\n            </p>\n          </motion.div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {keyTerms.map((term, index) => (\n              <motion.div\n                key={term.title}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n              >\n                <Card className=\"p-6 text-center h-full hover:shadow-lg transition-all duration-300\">\n                  <div className=\"inline-flex items-center justify-center w-12 h-12 bg-orange-100 rounded-full mb-4\">\n                    <term.icon className=\"h-6 w-6 text-orange-600\" />\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">{term.title}</h3>\n                  <p className=\"text-gray-600 text-sm leading-relaxed\">{term.description}</p>\n                </Card>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Terms Content */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <Card className=\"p-8 md:p-12\">\n              <div className=\"prose prose-lg max-w-none\">\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">1. Acceptance of Terms</h2>\n                <p className=\"text-gray-600 mb-8\">\n                  By accessing or using LifeManager's services, you agree to be bound by these Terms of Service \n                  and all applicable laws and regulations. If you do not agree with any of these terms, \n                  you are prohibited from using our services.\n                </p>\n\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">2. Description of Service</h2>\n                <p className=\"text-gray-600 mb-6\">\n                  LifeManager provides a comprehensive life management platform that includes:\n                </p>\n                <ul className=\"list-disc list-inside text-gray-600 mb-8 space-y-2\">\n                  <li>Task and project management tools</li>\n                  <li>Budget tracking and financial management</li>\n                  <li>Shopping list management</li>\n                  <li>AI-powered assistance and recommendations</li>\n                  <li>Recipe management and meal planning</li>\n                  <li>Cross-platform synchronization</li>\n                </ul>\n\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">3. User Accounts</h2>\n                <p className=\"text-gray-600 mb-6\">\n                  To use certain features of our service, you must create an account. You agree to:\n                </p>\n                <ul className=\"list-disc list-inside text-gray-600 mb-8 space-y-2\">\n                  <li>Provide accurate and complete information</li>\n                  <li>Maintain the security of your account credentials</li>\n                  <li>Notify us immediately of any unauthorized use</li>\n                  <li>Be responsible for all activities under your account</li>\n                </ul>\n\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">4. Acceptable Use</h2>\n                <p className=\"text-gray-600 mb-6\">\n                  You agree not to use our services to:\n                </p>\n                <ul className=\"list-disc list-inside text-gray-600 mb-8 space-y-2\">\n                  <li>Violate any applicable laws or regulations</li>\n                  <li>Infringe on intellectual property rights</li>\n                  <li>Transmit harmful or malicious content</li>\n                  <li>Attempt to gain unauthorized access to our systems</li>\n                  <li>Interfere with the proper functioning of our services</li>\n                  <li>Use our services for commercial purposes without permission</li>\n                </ul>\n\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">5. Content and Intellectual Property</h2>\n                <p className=\"text-gray-600 mb-6\">\n                  You retain ownership of content you create using our services. However, you grant us a \n                  limited license to use, store, and process your content to provide our services. \n                  Our services and underlying technology are protected by intellectual property laws.\n                </p>\n\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">6. Privacy</h2>\n                <p className=\"text-gray-600 mb-8\">\n                  Your privacy is important to us. Our collection and use of personal information is \n                  governed by our Privacy Policy, which is incorporated into these terms by reference.\n                </p>\n\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">7. Payment Terms</h2>\n                <p className=\"text-gray-600 mb-6\">\n                  For paid services:\n                </p>\n                <ul className=\"list-disc list-inside text-gray-600 mb-8 space-y-2\">\n                  <li>Fees are charged in advance on a recurring basis</li>\n                  <li>All fees are non-refundable except as required by law</li>\n                  <li>We may change our fees with 30 days' notice</li>\n                  <li>You are responsible for all taxes and fees</li>\n                </ul>\n\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">8. Service Availability</h2>\n                <p className=\"text-gray-600 mb-8\">\n                  We strive to maintain high service availability but cannot guarantee uninterrupted access. \n                  We may temporarily suspend service for maintenance, updates, or other operational reasons. \n                  We are not liable for any downtime or service interruptions.\n                </p>\n\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">9. Limitation of Liability</h2>\n                <p className=\"text-gray-600 mb-8\">\n                  To the maximum extent permitted by law, LifeManager shall not be liable for any indirect, \n                  incidental, special, consequential, or punitive damages, including but not limited to \n                  loss of profits, data, or other intangible losses resulting from your use of our services.\n                </p>\n\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">10. Termination</h2>\n                <p className=\"text-gray-600 mb-8\">\n                  Either party may terminate this agreement at any time. Upon termination, your right to \n                  use our services will cease immediately. We may retain certain information as required \n                  by law or for legitimate business purposes.\n                </p>\n\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">11. Changes to Terms</h2>\n                <p className=\"text-gray-600 mb-8\">\n                  We reserve the right to modify these terms at any time. We will notify users of material \n                  changes via email or through our service. Continued use of our services after changes \n                  constitutes acceptance of the new terms.\n                </p>\n\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">12. Governing Law</h2>\n                <p className=\"text-gray-600 mb-8\">\n                  These terms are governed by the laws of the State of California, United States, \n                  without regard to conflict of law principles. Any disputes will be resolved in \n                  the courts of San Francisco County, California.\n                </p>\n\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">13. Contact Information</h2>\n                <p className=\"text-gray-600 mb-4\">\n                  If you have questions about these terms, please contact us:\n                </p>\n                <ul className=\"list-none text-gray-600 space-y-2\">\n                  <li>Email: <EMAIL></li>\n                  <li>Address: 123 Tech Street, Suite 100, San Francisco, CA 94105</li>\n                  <li>Phone: +****************</li>\n                </ul>\n              </div>\n            </Card>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h3 className=\"text-2xl font-bold mb-4\">LifeManager</h3>\n            <div className=\"flex justify-center space-x-6 mb-6\">\n              <Link href=\"/\" className=\"text-gray-400 hover:text-white\">Home</Link>\n              <Link href=\"/about\" className=\"text-gray-400 hover:text-white\">About</Link>\n              <Link href=\"/features\" className=\"text-gray-400 hover:text-white\">Features</Link>\n              <Link href=\"/pricing\" className=\"text-gray-400 hover:text-white\">Pricing</Link>\n              <Link href=\"/contact\" className=\"text-gray-400 hover:text-white\">Contact</Link>\n              <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white\">Privacy</Link>\n            </div>\n            <p className=\"text-gray-500 text-sm\">\n              © 2024 LifeManager. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,WAAW;IACf;QACE,MAAM,wMAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,0MAAA,CAAA,aAAU;QAChB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,4NAAA,CAAA,oBAAiB;QACvB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,kNAAA,CAAA,eAAY;QAClB,OAAO;QACP,aAAa;IACf;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAgD;;;;;;kDAGzE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAgD;;;;;;kDAG9E,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxF,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAS,MAAK;gCAAK,WAAU;;kDAC1C,8OAAC,kNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAG3C,8OAAC;gCAAG,WAAU;;oCAAoD;kDAEhE,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAI1D,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;8CAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDAAG,WAAU;0DAA4C,KAAK,KAAK;;;;;;0DACpE,8OAAC;gDAAE,WAAU;0DAAyC,KAAK,WAAW;;;;;;;;;;;;mCAXnE,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;0BAoBzB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;kCAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAMlC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAGN,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAGN,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAGN,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAMlC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAKlC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAGN,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAMlC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAMlC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAMlC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAMlC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAMlC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShB,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAiC;;;;;;kDAC1D,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAiC;;;;;;kDAC/D,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAiC;;;;;;kDAClE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAiC;;;;;;kDACjE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAiC;;;;;;kDACjE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAiC;;;;;;;;;;;;0CAEnE,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}]}