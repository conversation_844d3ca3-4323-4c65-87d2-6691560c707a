{"version": 3, "sources": [], "sections": [{"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\nimport { cva, type VariantProps } from 'class-variance-authority'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed ring-offset-background transform hover:scale-105 active:scale-95 focus:scale-105',\n  {\n    variants: {\n      variant: {\n        default: 'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl focus-visible:ring-blue-500',\n        destructive: 'bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-lg hover:shadow-xl focus-visible:ring-red-500',\n        outline: 'border-2 border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus-visible:ring-gray-500',\n        secondary: 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-900 hover:from-gray-200 hover:to-gray-300 focus-visible:ring-gray-500',\n        ghost: 'hover:bg-gray-100 text-gray-700 focus-visible:ring-gray-500',\n        link: 'underline-offset-4 hover:underline text-blue-600 focus-visible:ring-blue-500',\n        success: 'bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl focus-visible:ring-green-500',\n        warning: 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white hover:from-yellow-600 hover:to-yellow-700 shadow-lg hover:shadow-xl focus-visible:ring-yellow-500',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 text-xs',\n        lg: 'h-12 px-8 text-base',\n        xl: 'h-14 px-10 text-lg',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean\n  loadingText?: string\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, loadingText, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        aria-disabled={disabled || loading}\n        {...props}\n      >\n        {loading ? (\n          <>\n            <svg\n              className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              aria-hidden=\"true\"\n            >\n              <circle\n                className=\"opacity-25\"\n                cx=\"12\"\n                cy=\"12\"\n                r=\"10\"\n                stroke=\"currentColor\"\n                strokeWidth=\"4\"\n              ></circle>\n              <path\n                className=\"opacity-75\"\n                fill=\"currentColor\"\n                d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n              ></path>\n            </svg>\n            {loadingText || 'Loading...'}\n          </>\n        ) : (\n          children\n        )}\n      </button>\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,gVACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACjF,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACtB,iBAAe,YAAY;QAC1B,GAAG,KAAK;kBAER,wBACC;;8BACE,8OAAC;oBACC,WAAU;oBACV,OAAM;oBACN,MAAK;oBACL,SAAQ;oBACR,eAAY;;sCAEZ,8OAAC;4BACC,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;;;;;;sCAEd,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;gBAGL,eAAe;;2BAGlB;;;;;;AAIR;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Card.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nconst Card = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border border-gray-200 bg-white text-gray-950 shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCard.displayName = 'Card'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex flex-col space-y-1.5 p-6', className)} {...props} />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('text-lg font-semibold leading-none tracking-tight text-gray-900', className)}\n      {...props}\n    />\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p ref={ref} className={cn('text-sm text-gray-600', className)} {...props} />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex items-center p-6 pt-0', className)} {...props} />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACpB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAGvF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAE,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QAAa,GAAG,KAAK;;;;;;AAG7E,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAGlE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAGpF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Badge.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { cn } from '@/lib/utils'\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-all duration-200',\n  {\n    variants: {\n      variant: {\n        default: 'bg-blue-100 text-blue-800 hover:bg-blue-200',\n        secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200',\n        destructive: 'bg-red-100 text-red-800 hover:bg-red-200',\n        success: 'bg-green-100 text-green-800 hover:bg-green-200',\n        warning: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',\n        outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50',\n        purple: 'bg-purple-100 text-purple-800 hover:bg-purple-200',\n        indigo: 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200',\n        pink: 'bg-pink-100 text-pink-800 hover:bg-pink-200',\n        orange: 'bg-orange-100 text-orange-800 hover:bg-orange-200',\n      },\n      size: {\n        default: 'px-2.5 py-0.5 text-xs',\n        sm: 'px-2 py-0.5 text-xs',\n        lg: 'px-3 py-1 text-sm',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nconst Badge = forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant, size, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(badgeVariants({ variant, size }), className)}\n        {...props}\n      />\n    )\n  }\n)\nBadge.displayName = 'Badge'\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,uGACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,QAAQ;QACV;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IACvC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAC/C,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/TestimonialsCarousel.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { ChevronLeftIcon, ChevronRightIcon, StarIcon, PlayIcon, PauseIcon } from 'lucide-react'\nimport { But<PERSON> } from './Button'\n\ninterface Testimonial {\n  name: string\n  role: string\n  location: string\n  image: string\n  content: string\n  rating: number\n}\n\ninterface TestimonialsCarouselProps {\n  testimonials: Testimonial[]\n  autoPlay?: boolean\n  autoPlayInterval?: number\n  showControls?: boolean\n  showIndicators?: boolean\n}\n\nexport default function TestimonialsCarousel({\n  testimonials,\n  autoPlay = true,\n  autoPlayInterval = 5000,\n  showControls = true,\n  showIndicators = true\n}: TestimonialsCarouselProps) {\n  const [currentIndex, setCurrentIndex] = useState(0)\n  const [isPlaying, setIsPlaying] = useState(autoPlay)\n  const [direction, setDirection] = useState(0)\n\n  const nextTestimonial = useCallback(() => {\n    setDirection(1)\n    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length)\n  }, [testimonials.length])\n\n  const prevTestimonial = useCallback(() => {\n    setDirection(-1)\n    setCurrentIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length)\n  }, [testimonials.length])\n\n  const goToTestimonial = useCallback((index: number) => {\n    setDirection(index > currentIndex ? 1 : -1)\n    setCurrentIndex(index)\n  }, [currentIndex])\n\n  const togglePlayPause = () => {\n    setIsPlaying(!isPlaying)\n  }\n\n  useEffect(() => {\n    if (!isPlaying || testimonials.length <= 1) return\n\n    const interval = setInterval(nextTestimonial, autoPlayInterval)\n    return () => clearInterval(interval)\n  }, [isPlaying, nextTestimonial, autoPlayInterval, testimonials.length])\n\n  const slideVariants = {\n    enter: (direction: number) => ({\n      x: direction > 0 ? 1000 : -1000,\n      opacity: 0\n    }),\n    center: {\n      zIndex: 1,\n      x: 0,\n      opacity: 1\n    },\n    exit: (direction: number) => ({\n      zIndex: 0,\n      x: direction < 0 ? 1000 : -1000,\n      opacity: 0\n    })\n  }\n\n  const swipeConfidenceThreshold = 10000\n  const swipePower = (offset: number, velocity: number) => {\n    return Math.abs(offset) * velocity\n  }\n\n  if (testimonials.length === 0) return null\n\n  return (\n    <div className=\"relative w-full max-w-4xl mx-auto\">\n      {/* Main Carousel */}\n      <div className=\"relative h-96 overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-100\">\n        <AnimatePresence initial={false} custom={direction}>\n          <motion.div\n            key={currentIndex}\n            custom={direction}\n            variants={slideVariants}\n            initial=\"enter\"\n            animate=\"center\"\n            exit=\"exit\"\n            transition={{\n              x: { type: \"spring\", stiffness: 300, damping: 30 },\n              opacity: { duration: 0.2 }\n            }}\n            drag=\"x\"\n            dragConstraints={{ left: 0, right: 0 }}\n            dragElastic={1}\n            onDragEnd={(e, { offset, velocity }) => {\n              const swipe = swipePower(offset.x, velocity.x)\n\n              if (swipe < -swipeConfidenceThreshold) {\n                nextTestimonial()\n              } else if (swipe > swipeConfidenceThreshold) {\n                prevTestimonial()\n              }\n            }}\n            className=\"absolute inset-0 flex items-center justify-center p-8 cursor-grab active:cursor-grabbing\"\n          >\n            <div className=\"text-center max-w-2xl\">\n              {/* Rating Stars */}\n              <div className=\"flex justify-center mb-6\">\n                {[...Array(testimonials[currentIndex].rating)].map((_, i) => (\n                  <motion.div\n                    key={i}\n                    initial={{ opacity: 0, scale: 0 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ delay: i * 0.1 }}\n                  >\n                    <StarIcon className=\"h-6 w-6 text-yellow-400 fill-current mx-1\" />\n                  </motion.div>\n                ))}\n              </div>\n\n              {/* Testimonial Content */}\n              <motion.blockquote\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n                className=\"text-xl md:text-2xl text-gray-700 font-medium italic mb-8 leading-relaxed\"\n              >\n                \"{testimonials[currentIndex].content}\"\n              </motion.blockquote>\n\n              {/* Author Info */}\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.5 }}\n                className=\"flex items-center justify-center\"\n              >\n                <div className=\"w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4\">\n                  {testimonials[currentIndex].name.split(' ').map(n => n[0]).join('')}\n                </div>\n                <div className=\"text-left\">\n                  <div className=\"font-semibold text-gray-900 text-lg\">\n                    {testimonials[currentIndex].name}\n                  </div>\n                  <div className=\"text-gray-600\">\n                    {testimonials[currentIndex].role}\n                  </div>\n                  <div className=\"text-sm text-gray-500\">\n                    {testimonials[currentIndex].location}\n                  </div>\n                </div>\n              </motion.div>\n            </div>\n          </motion.div>\n        </AnimatePresence>\n      </div>\n\n      {/* Navigation Controls */}\n      {showControls && testimonials.length > 1 && (\n        <div className=\"absolute inset-y-0 left-0 right-0 flex items-center justify-between pointer-events-none\">\n          <motion.button\n            onClick={prevTestimonial}\n            className=\"ml-4 p-3 bg-white/90 hover:bg-white rounded-full shadow-lg backdrop-blur-sm pointer-events-auto transition-all duration-200\"\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n          >\n            <ChevronLeftIcon className=\"h-6 w-6 text-gray-700\" />\n          </motion.button>\n          \n          <motion.button\n            onClick={nextTestimonial}\n            className=\"mr-4 p-3 bg-white/90 hover:bg-white rounded-full shadow-lg backdrop-blur-sm pointer-events-auto transition-all duration-200\"\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n          >\n            <ChevronRightIcon className=\"h-6 w-6 text-gray-700\" />\n          </motion.button>\n        </div>\n      )}\n\n      {/* Indicators */}\n      {showIndicators && testimonials.length > 1 && (\n        <div className=\"flex justify-center mt-6 space-x-2\">\n          {testimonials.map((_, index) => (\n            <motion.button\n              key={index}\n              onClick={() => goToTestimonial(index)}\n              className={`w-3 h-3 rounded-full transition-all duration-200 ${\n                index === currentIndex\n                  ? 'bg-blue-600 scale-125'\n                  : 'bg-gray-300 hover:bg-gray-400'\n              }`}\n              whileHover={{ scale: 1.2 }}\n              whileTap={{ scale: 0.9 }}\n            />\n          ))}\n        </div>\n      )}\n\n      {/* Play/Pause Control */}\n      {autoPlay && testimonials.length > 1 && (\n        <div className=\"flex justify-center mt-4\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={togglePlayPause}\n            className=\"flex items-center space-x-2\"\n          >\n            {isPlaying ? (\n              <PauseIcon className=\"h-4 w-4\" />\n            ) : (\n              <PlayIcon className=\"h-4 w-4\" />\n            )}\n            <span>{isPlaying ? 'Pause' : 'Play'}</span>\n          </Button>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAwBe,SAAS,qBAAqB,EAC3C,YAAY,EACZ,WAAW,IAAI,EACf,mBAAmB,IAAI,EACvB,eAAe,IAAI,EACnB,iBAAiB,IAAI,EACK;IAC1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,aAAa;QACb,gBAAgB,CAAC,YAAc,CAAC,YAAY,CAAC,IAAI,aAAa,MAAM;IACtE,GAAG;QAAC,aAAa,MAAM;KAAC;IAExB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,aAAa,CAAC;QACd,gBAAgB,CAAC,YAAc,CAAC,YAAY,IAAI,aAAa,MAAM,IAAI,aAAa,MAAM;IAC5F,GAAG;QAAC,aAAa,MAAM;KAAC;IAExB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,aAAa,QAAQ,eAAe,IAAI,CAAC;QACzC,gBAAgB;IAClB,GAAG;QAAC;KAAa;IAEjB,MAAM,kBAAkB;QACtB,aAAa,CAAC;IAChB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,aAAa,MAAM,IAAI,GAAG;QAE5C,MAAM,WAAW,YAAY,iBAAiB;QAC9C,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAW;QAAiB;QAAkB,aAAa,MAAM;KAAC;IAEtE,MAAM,gBAAgB;QACpB,OAAO,CAAC,YAAsB,CAAC;gBAC7B,GAAG,YAAY,IAAI,OAAO,CAAC;gBAC3B,SAAS;YACX,CAAC;QACD,QAAQ;YACN,QAAQ;YACR,GAAG;YACH,SAAS;QACX;QACA,MAAM,CAAC,YAAsB,CAAC;gBAC5B,QAAQ;gBACR,GAAG,YAAY,IAAI,OAAO,CAAC;gBAC3B,SAAS;YACX,CAAC;IACH;IAEA,MAAM,2BAA2B;IACjC,MAAM,aAAa,CAAC,QAAgB;QAClC,OAAO,KAAK,GAAG,CAAC,UAAU;IAC5B;IAEA,IAAI,aAAa,MAAM,KAAK,GAAG,OAAO;IAEtC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;oBAAC,SAAS;oBAAO,QAAQ;8BACvC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,QAAQ;wBACR,UAAU;wBACV,SAAQ;wBACR,SAAQ;wBACR,MAAK;wBACL,YAAY;4BACV,GAAG;gCAAE,MAAM;gCAAU,WAAW;gCAAK,SAAS;4BAAG;4BACjD,SAAS;gCAAE,UAAU;4BAAI;wBAC3B;wBACA,MAAK;wBACL,iBAAiB;4BAAE,MAAM;4BAAG,OAAO;wBAAE;wBACrC,aAAa;wBACb,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE;4BACjC,MAAM,QAAQ,WAAW,OAAO,CAAC,EAAE,SAAS,CAAC;4BAE7C,IAAI,QAAQ,CAAC,0BAA0B;gCACrC;4BACF,OAAO,IAAI,QAAQ,0BAA0B;gCAC3C;4BACF;wBACF;wBACA,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM,YAAY,CAAC,aAAa,CAAC,MAAM;qCAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,YAAY;gDAAE,OAAO,IAAI;4CAAI;sDAE7B,cAAA,8OAAC,sMAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;2CALf;;;;;;;;;;8CAWX,8OAAC,0LAAA,CAAA,SAAM,CAAC,UAAU;oCAChB,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;;wCACX;wCACG,YAAY,CAAC,aAAa,CAAC,OAAO;wCAAC;;;;;;;8CAIvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDACZ,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;sDAElE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,YAAY,CAAC,aAAa,CAAC,IAAI;;;;;;8DAElC,8OAAC;oDAAI,WAAU;8DACZ,YAAY,CAAC,aAAa,CAAC,IAAI;;;;;;8DAElC,8OAAC;oDAAI,WAAU;8DACZ,YAAY,CAAC,aAAa,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;uBAnEvC;;;;;;;;;;;;;;;YA6EV,gBAAgB,aAAa,MAAM,GAAG,mBACrC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;wBACT,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAI;wBACzB,UAAU;4BAAE,OAAO;wBAAI;kCAEvB,cAAA,8OAAC,wNAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;;;;;;kCAG7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;wBACT,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAI;wBACzB,UAAU;4BAAE,OAAO;wBAAI;kCAEvB,cAAA,8OAAC,0NAAA,CAAA,mBAAgB;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAMjC,kBAAkB,aAAa,MAAM,GAAG,mBACvC,8OAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBAEZ,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,0BACA,iCACJ;wBACF,YAAY;4BAAE,OAAO;wBAAI;wBACzB,UAAU;4BAAE,OAAO;wBAAI;uBARlB;;;;;;;;;;YAeZ,YAAY,aAAa,MAAM,GAAG,mBACjC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;;wBAET,0BACC,8OAAC,wMAAA,CAAA,YAAS;4BAAC,WAAU;;;;;iDAErB,8OAAC,sMAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCAEtB,8OAAC;sCAAM,YAAY,UAAU;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}, {"offset": {"line": 770, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/PricingSection.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { CheckIcon, XIcon, StarIcon, ZapIcon, CrownIcon, BuildingIcon } from 'lucide-react'\nimport { Button } from './Button'\nimport { Badge } from './Badge'\nimport { Card } from './Card'\n\ninterface PricingFeature {\n  name: string\n  free: boolean | string\n  pro: boolean | string\n  enterprise: boolean | string\n  category?: string\n}\n\ninterface PricingPlan {\n  name: string\n  monthlyPrice: number\n  yearlyPrice: number\n  description: string\n  features: string[]\n  popular: boolean\n  icon: any\n  color: string\n  ctaText: string\n}\n\nconst pricingFeatures: PricingFeature[] = [\n  // Core Features\n  { name: 'Tasks & Projects', free: '50 tasks', pro: 'Unlimited', enterprise: 'Unlimited', category: 'Core Features' },\n  { name: 'Budget Tracking', free: 'Basic', pro: 'Advanced Analytics', enterprise: 'Enterprise Reports', category: 'Core Features' },\n  { name: 'Shopping Lists', free: '3 lists', pro: 'Unlimited', enterprise: 'Unlimited', category: 'Core Features' },\n  { name: 'Recipe Management', free: '10 recipes', pro: 'Unlimited', enterprise: 'Unlimited', category: 'Core Features' },\n  { name: 'Mobile & Web Access', free: true, pro: true, enterprise: true, category: 'Core Features' },\n  \n  // AI & Intelligence\n  { name: 'AI Chat Assistant', free: false, pro: true, enterprise: true, category: 'AI & Intelligence' },\n  { name: 'Smart Recommendations', free: false, pro: true, enterprise: true, category: 'AI & Intelligence' },\n  { name: 'Predictive Analytics', free: false, pro: 'Basic', enterprise: 'Advanced', category: 'AI & Intelligence' },\n  { name: 'Auto-categorization', free: false, pro: true, enterprise: true, category: 'AI & Intelligence' },\n  \n  // Collaboration\n  { name: 'Team Collaboration', free: false, pro: '5 members', enterprise: 'Unlimited', category: 'Collaboration' },\n  { name: 'Shared Lists & Budgets', free: false, pro: true, enterprise: true, category: 'Collaboration' },\n  { name: 'Real-time Sync', free: 'Basic', pro: 'Advanced', enterprise: 'Enterprise', category: 'Collaboration' },\n  \n  // Support & Security\n  { name: 'Customer Support', free: 'Community', pro: 'Priority Email', enterprise: 'Dedicated Manager', category: 'Support & Security' },\n  { name: 'Data Export', free: false, pro: true, enterprise: true, category: 'Support & Security' },\n  { name: 'Advanced Security', free: false, pro: 'Standard', enterprise: 'Enterprise Grade', category: 'Support & Security' },\n  { name: 'SLA Guarantee', free: false, pro: false, enterprise: '99.9%', category: 'Support & Security' },\n]\n\nconst plans: PricingPlan[] = [\n  {\n    name: 'Free',\n    monthlyPrice: 0,\n    yearlyPrice: 0,\n    description: 'Perfect for getting started',\n    features: ['50 tasks', 'Basic budget tracking', '3 shopping lists', 'Mobile & web access', 'Community support'],\n    popular: false,\n    icon: StarIcon,\n    color: 'from-gray-500 to-gray-600',\n    ctaText: 'Get Started Free'\n  },\n  {\n    name: 'Pro',\n    monthlyPrice: 9.99,\n    yearlyPrice: 99.99,\n    description: 'For power users and families',\n    features: ['Unlimited tasks & projects', 'Advanced analytics', 'AI-powered insights', 'Team collaboration', 'Priority support'],\n    popular: true,\n    icon: ZapIcon,\n    color: 'from-blue-500 to-purple-600',\n    ctaText: 'Start Pro Trial'\n  },\n  {\n    name: 'Enterprise',\n    monthlyPrice: 29.99,\n    yearlyPrice: 299.99,\n    description: 'For organizations and teams',\n    features: ['Everything in Pro', 'Custom integrations', 'Advanced security', 'Dedicated support', 'SLA guarantee'],\n    popular: false,\n    icon: BuildingIcon,\n    color: 'from-purple-500 to-indigo-600',\n    ctaText: 'Contact Sales'\n  }\n]\n\nexport default function PricingSection() {\n  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly')\n  const [showComparison, setShowComparison] = useState(false)\n  const [selectedCategory, setSelectedCategory] = useState<string>('All')\n\n  const categories = ['All', ...Array.from(new Set(pricingFeatures.map(f => f.category)))]\n  const filteredFeatures = selectedCategory === 'All' \n    ? pricingFeatures \n    : pricingFeatures.filter(f => f.category === selectedCategory)\n\n  const getPrice = (plan: PricingPlan) => {\n    if (plan.monthlyPrice === 0) return '$0'\n    return billingPeriod === 'monthly' \n      ? `$${plan.monthlyPrice}` \n      : `$${(plan.yearlyPrice / 12).toFixed(2)}`\n  }\n\n  const getSavings = (plan: PricingPlan) => {\n    if (plan.monthlyPrice === 0) return null\n    const monthlyCost = plan.monthlyPrice * 12\n    const savings = monthlyCost - plan.yearlyPrice\n    return Math.round((savings / monthlyCost) * 100)\n  }\n\n  const renderFeatureValue = (value: boolean | string) => {\n    if (value === true) return <CheckIcon className=\"h-5 w-5 text-green-500\" />\n    if (value === false) return <XIcon className=\"h-5 w-5 text-gray-300\" />\n    return <span className=\"text-sm text-gray-600\">{value}</span>\n  }\n\n  return (\n    <div className=\"w-full\">\n      {/* Billing Toggle */}\n      <div className=\"flex justify-center mb-8\">\n        <div className=\"bg-gray-100 p-1 rounded-xl\">\n          <div className=\"flex items-center space-x-1\">\n            <button\n              onClick={() => setBillingPeriod('monthly')}\n              className={`px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\n                billingPeriod === 'monthly'\n                  ? 'bg-white text-gray-900 shadow-sm'\n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              Monthly\n            </button>\n            <button\n              onClick={() => setBillingPeriod('yearly')}\n              className={`px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 relative ${\n                billingPeriod === 'yearly'\n                  ? 'bg-white text-gray-900 shadow-sm'\n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              Yearly\n              <Badge variant=\"success\" size=\"sm\" className=\"absolute -top-2 -right-2 text-xs\">\n                Save 20%\n              </Badge>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Pricing Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\">\n        {plans.map((plan, index) => {\n          const savings = getSavings(plan)\n          return (\n            <motion.div\n              key={plan.name}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              whileHover={{ scale: plan.popular ? 1.02 : 1.05 }}\n              className=\"relative\"\n            >\n              {plan.popular && (\n                <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 z-10\">\n                  <Badge variant=\"default\" className=\"px-4 py-1 bg-gradient-to-r from-blue-500 to-purple-600\">\n                    <CrownIcon className=\"h-3 w-3 mr-1\" />\n                    Most Popular\n                  </Badge>\n                </div>\n              )}\n              \n              {billingPeriod === 'yearly' && savings && (\n                <div className=\"absolute -top-2 -right-2 z-10\">\n                  <Badge variant=\"success\" size=\"sm\">\n                    Save {savings}%\n                  </Badge>\n                </div>\n              )}\n\n              <Card className={`h-full p-8 text-center relative overflow-hidden ${\n                plan.popular ? 'border-2 border-blue-500 shadow-xl' : ''\n              }`}>\n                <div className={`absolute inset-0 bg-gradient-to-br ${plan.color} opacity-5`} />\n                \n                <div className=\"relative z-10\">\n                  <div className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r ${plan.color} rounded-full mb-4`}>\n                    <plan.icon className=\"h-8 w-8 text-white\" />\n                  </div>\n                  \n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">{plan.name}</h3>\n                  \n                  <div className=\"mb-4\">\n                    <span className=\"text-4xl font-bold text-gray-900\">{getPrice(plan)}</span>\n                    {plan.monthlyPrice > 0 && (\n                      <span className=\"text-gray-600 ml-2\">\n                        /{billingPeriod === 'monthly' ? 'month' : 'month, billed yearly'}\n                      </span>\n                    )}\n                  </div>\n                  \n                  <p className=\"text-gray-600 mb-6\">{plan.description}</p>\n                  \n                  <ul className=\"space-y-3 mb-8 text-left\">\n                    {plan.features.map((feature, featureIndex) => (\n                      <li key={featureIndex} className=\"flex items-center\">\n                        <CheckIcon className=\"h-5 w-5 text-green-500 mr-3 flex-shrink-0\" />\n                        <span className=\"text-gray-700\">{feature}</span>\n                      </li>\n                    ))}\n                  </ul>\n                  \n                  <Button \n                    variant={plan.popular ? \"default\" : \"outline\"} \n                    size=\"lg\" \n                    className=\"w-full\"\n                  >\n                    {plan.ctaText}\n                  </Button>\n                </div>\n              </Card>\n            </motion.div>\n          )\n        })}\n      </div>\n\n      {/* Feature Comparison Toggle */}\n      <div className=\"text-center mb-8\">\n        <Button\n          variant=\"outline\"\n          onClick={() => setShowComparison(!showComparison)}\n          className=\"mx-auto\"\n        >\n          {showComparison ? 'Hide' : 'Show'} Detailed Comparison\n        </Button>\n      </div>\n\n      {/* Detailed Feature Comparison */}\n      <AnimatePresence>\n        {showComparison && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"overflow-hidden\"\n          >\n            <Card className=\"p-6\">\n              {/* Category Filter */}\n              <div className=\"flex flex-wrap justify-center gap-2 mb-6\">\n                {categories.map((category) => (\n                  <button\n                    key={category}\n                    onClick={() => setSelectedCategory(category)}\n                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\n                      selectedCategory === category\n                        ? 'bg-blue-100 text-blue-700'\n                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\n                    }`}\n                  >\n                    {category}\n                  </button>\n                ))}\n              </div>\n\n              {/* Comparison Table */}\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full\">\n                  <thead>\n                    <tr className=\"border-b border-gray-200\">\n                      <th className=\"text-left py-4 px-4 font-semibold text-gray-900\">Features</th>\n                      <th className=\"text-center py-4 px-4 font-semibold text-gray-900\">Free</th>\n                      <th className=\"text-center py-4 px-4 font-semibold text-gray-900\">Pro</th>\n                      <th className=\"text-center py-4 px-4 font-semibold text-gray-900\">Enterprise</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {filteredFeatures.map((feature, index) => (\n                      <motion.tr\n                        key={feature.name}\n                        initial={{ opacity: 0 }}\n                        animate={{ opacity: 1 }}\n                        transition={{ delay: index * 0.05 }}\n                        className=\"border-b border-gray-100 hover:bg-gray-50\"\n                      >\n                        <td className=\"py-4 px-4 font-medium text-gray-900\">{feature.name}</td>\n                        <td className=\"py-4 px-4 text-center\">{renderFeatureValue(feature.free)}</td>\n                        <td className=\"py-4 px-4 text-center\">{renderFeatureValue(feature.pro)}</td>\n                        <td className=\"py-4 px-4 text-center\">{renderFeatureValue(feature.enterprise)}</td>\n                      </motion.tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            </Card>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AA6BA,MAAM,kBAAoC;IACxC,gBAAgB;IAChB;QAAE,MAAM;QAAoB,MAAM;QAAY,KAAK;QAAa,YAAY;QAAa,UAAU;IAAgB;IACnH;QAAE,MAAM;QAAmB,MAAM;QAAS,KAAK;QAAsB,YAAY;QAAsB,UAAU;IAAgB;IACjI;QAAE,MAAM;QAAkB,MAAM;QAAW,KAAK;QAAa,YAAY;QAAa,UAAU;IAAgB;IAChH;QAAE,MAAM;QAAqB,MAAM;QAAc,KAAK;QAAa,YAAY;QAAa,UAAU;IAAgB;IACtH;QAAE,MAAM;QAAuB,MAAM;QAAM,KAAK;QAAM,YAAY;QAAM,UAAU;IAAgB;IAElG,oBAAoB;IACpB;QAAE,MAAM;QAAqB,MAAM;QAAO,KAAK;QAAM,YAAY;QAAM,UAAU;IAAoB;IACrG;QAAE,MAAM;QAAyB,MAAM;QAAO,KAAK;QAAM,YAAY;QAAM,UAAU;IAAoB;IACzG;QAAE,MAAM;QAAwB,MAAM;QAAO,KAAK;QAAS,YAAY;QAAY,UAAU;IAAoB;IACjH;QAAE,MAAM;QAAuB,MAAM;QAAO,KAAK;QAAM,YAAY;QAAM,UAAU;IAAoB;IAEvG,gBAAgB;IAChB;QAAE,MAAM;QAAsB,MAAM;QAAO,KAAK;QAAa,YAAY;QAAa,UAAU;IAAgB;IAChH;QAAE,MAAM;QAA0B,MAAM;QAAO,KAAK;QAAM,YAAY;QAAM,UAAU;IAAgB;IACtG;QAAE,MAAM;QAAkB,MAAM;QAAS,KAAK;QAAY,YAAY;QAAc,UAAU;IAAgB;IAE9G,qBAAqB;IACrB;QAAE,MAAM;QAAoB,MAAM;QAAa,KAAK;QAAkB,YAAY;QAAqB,UAAU;IAAqB;IACtI;QAAE,MAAM;QAAe,MAAM;QAAO,KAAK;QAAM,YAAY;QAAM,UAAU;IAAqB;IAChG;QAAE,MAAM;QAAqB,MAAM;QAAO,KAAK;QAAY,YAAY;QAAoB,UAAU;IAAqB;IAC1H;QAAE,MAAM;QAAiB,MAAM;QAAO,KAAK;QAAO,YAAY;QAAS,UAAU;IAAqB;CACvG;AAED,MAAM,QAAuB;IAC3B;QACE,MAAM;QACN,cAAc;QACd,aAAa;QACb,aAAa;QACb,UAAU;YAAC;YAAY;YAAyB;YAAoB;YAAuB;SAAoB;QAC/G,SAAS;QACT,MAAM,sMAAA,CAAA,WAAQ;QACd,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM;QACN,cAAc;QACd,aAAa;QACb,aAAa;QACb,UAAU;YAAC;YAA8B;YAAsB;YAAuB;YAAsB;SAAmB;QAC/H,SAAS;QACT,MAAM,oMAAA,CAAA,UAAO;QACb,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM;QACN,cAAc;QACd,aAAa;QACb,aAAa;QACb,UAAU;YAAC;YAAqB;YAAuB;YAAqB;YAAqB;SAAgB;QACjH,SAAS;QACT,MAAM,8MAAA,CAAA,eAAY;QAClB,OAAO;QACP,SAAS;IACX;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACzE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,MAAM,aAAa;QAAC;WAAU,MAAM,IAAI,CAAC,IAAI,IAAI,gBAAgB,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;KAAI;IACxF,MAAM,mBAAmB,qBAAqB,QAC1C,kBACA,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;IAE/C,MAAM,WAAW,CAAC;QAChB,IAAI,KAAK,YAAY,KAAK,GAAG,OAAO;QACpC,OAAO,kBAAkB,YACrB,CAAC,CAAC,EAAE,KAAK,YAAY,EAAE,GACvB,CAAC,CAAC,EAAE,CAAC,KAAK,WAAW,GAAG,EAAE,EAAE,OAAO,CAAC,IAAI;IAC9C;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,KAAK,YAAY,KAAK,GAAG,OAAO;QACpC,MAAM,cAAc,KAAK,YAAY,GAAG;QACxC,MAAM,UAAU,cAAc,KAAK,WAAW;QAC9C,OAAO,KAAK,KAAK,CAAC,AAAC,UAAU,cAAe;IAC9C;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,UAAU,MAAM,qBAAO,8OAAC,wMAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAChD,IAAI,UAAU,OAAO,qBAAO,8OAAC,gMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAC7C,qBAAO,8OAAC;YAAK,WAAU;sBAAyB;;;;;;IAClD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAW,CAAC,qEAAqE,EAC/E,kBAAkB,YACd,qCACA,qCACJ;0CACH;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAW,CAAC,8EAA8E,EACxF,kBAAkB,WACd,qCACA,qCACJ;;oCACH;kDAEC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxF,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM;oBAChB,MAAM,UAAU,WAAW;oBAC3B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ;wBAAI;wBAChD,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,OAAO,KAAK,OAAO,GAAG,OAAO;wBAAK;wBAChD,WAAU;;4BAET,KAAK,OAAO,kBACX,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;sDACjC,8OAAC,wMAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;4BAM3C,kBAAkB,YAAY,yBAC7B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,MAAK;;wCAAK;wCAC3B;wCAAQ;;;;;;;;;;;;0CAKpB,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAW,CAAC,gDAAgD,EAChE,KAAK,OAAO,GAAG,uCAAuC,IACtD;;kDACA,8OAAC;wCAAI,WAAW,CAAC,mCAAmC,EAAE,KAAK,KAAK,CAAC,UAAU,CAAC;;;;;;kDAE5E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,mEAAmE,EAAE,KAAK,KAAK,CAAC,kBAAkB,CAAC;0DAClH,cAAA,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAGvB,8OAAC;gDAAG,WAAU;0DAAyC,KAAK,IAAI;;;;;;0DAEhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAoC,SAAS;;;;;;oDAC5D,KAAK,YAAY,GAAG,mBACnB,8OAAC;wDAAK,WAAU;;4DAAqB;4DACjC,kBAAkB,YAAY,UAAU;;;;;;;;;;;;;0DAKhD,8OAAC;gDAAE,WAAU;0DAAsB,KAAK,WAAW;;;;;;0DAEnD,8OAAC;gDAAG,WAAU;0DACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,8OAAC;wDAAsB,WAAU;;0EAC/B,8OAAC,wMAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,8OAAC;gEAAK,WAAU;0EAAiB;;;;;;;uDAF1B;;;;;;;;;;0DAOb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,KAAK,OAAO,GAAG,YAAY;gDACpC,MAAK;gDACL,WAAU;0DAET,KAAK,OAAO;;;;;;;;;;;;;;;;;;;uBA9Dd,KAAK,IAAI;;;;;gBAoEpB;;;;;;0BAIF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,SAAS,IAAM,kBAAkB,CAAC;oBAClC,WAAU;;wBAET,iBAAiB,SAAS;wBAAO;;;;;;;;;;;;0BAKtC,8OAAC,yLAAA,CAAA,kBAAe;0BACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CAEd,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;wCAEC,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,qEAAqE,EAC/E,qBAAqB,WACjB,8BACA,uDACJ;kDAED;uCARI;;;;;;;;;;0CAcX,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;sDACC,cAAA,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAAkD;;;;;;kEAChE,8OAAC;wDAAG,WAAU;kEAAoD;;;;;;kEAClE,8OAAC;wDAAG,WAAU;kEAAoD;;;;;;kEAClE,8OAAC;wDAAG,WAAU;kEAAoD;;;;;;;;;;;;;;;;;sDAGtE,8OAAC;sDACE,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oDAER,SAAS;wDAAE,SAAS;oDAAE;oDACtB,SAAS;wDAAE,SAAS;oDAAE;oDACtB,YAAY;wDAAE,OAAO,QAAQ;oDAAK;oDAClC,WAAU;;sEAEV,8OAAC;4DAAG,WAAU;sEAAuC,QAAQ,IAAI;;;;;;sEACjE,8OAAC;4DAAG,WAAU;sEAAyB,mBAAmB,QAAQ,IAAI;;;;;;sEACtE,8OAAC;4DAAG,WAAU;sEAAyB,mBAAmB,QAAQ,GAAG;;;;;;sEACrE,8OAAC;4DAAG,WAAU;sEAAyB,mBAAmB,QAAQ,UAAU;;;;;;;mDATvE,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBzC", "debugId": null}}, {"offset": {"line": 1475, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/AnimatedCounter.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { motion, useInView, useMotionValue, useSpring } from 'framer-motion'\n\ninterface AnimatedCounterProps {\n  value: number\n  duration?: number\n  suffix?: string\n  prefix?: string\n  decimals?: number\n  className?: string\n}\n\nexport default function AnimatedCounter({\n  value,\n  duration = 2,\n  suffix = '',\n  prefix = '',\n  decimals = 0,\n  className = ''\n}: AnimatedCounterProps) {\n  const ref = useRef<HTMLSpanElement>(null)\n  const motionValue = useMotionValue(0)\n  const springValue = useSpring(motionValue, { duration: duration * 1000 })\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" })\n  const [displayValue, setDisplayValue] = useState(0)\n\n  useEffect(() => {\n    if (isInView) {\n      motionValue.set(value)\n    }\n  }, [motionValue, isInView, value])\n\n  useEffect(() => {\n    const unsubscribe = springValue.on('change', (latest) => {\n      setDisplayValue(latest)\n    })\n    return unsubscribe\n  }, [springValue])\n\n  const formatNumber = (num: number) => {\n    if (decimals > 0) {\n      return num.toFixed(decimals)\n    }\n    return Math.floor(num).toLocaleString()\n  }\n\n  return (\n    <span ref={ref} className={className}>\n      {prefix}{formatNumber(displayValue)}{suffix}\n    </span>\n  )\n}\n\n// Specialized counter for different types of stats\nexport function PercentageCounter({ value, ...props }: Omit<AnimatedCounterProps, 'suffix'>) {\n  return <AnimatedCounter value={value} suffix=\"%\" {...props} />\n}\n\nexport function CurrencyCounter({ value, currency = '$', ...props }: Omit<AnimatedCounterProps, 'prefix'> & { currency?: string }) {\n  return <AnimatedCounter value={value} prefix={currency} {...props} />\n}\n\nexport function LargeNumberCounter({ value, ...props }: AnimatedCounterProps) {\n  const formatLargeNumber = (num: number) => {\n    if (num >= 1000000) {\n      return `${(num / 1000000).toFixed(1)}M`\n    }\n    if (num >= 1000) {\n      return `${(num / 1000).toFixed(1)}K`\n    }\n    return num.toString()\n  }\n\n  // Override the display to use our custom formatting\n  const ref = useRef<HTMLSpanElement>(null)\n  const motionValue = useMotionValue(0)\n  const springValue = useSpring(motionValue, { duration: (props.duration || 2) * 1000 })\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" })\n  const [displayValue, setDisplayValue] = useState(0)\n\n  useEffect(() => {\n    if (isInView) {\n      motionValue.set(value)\n    }\n  }, [motionValue, isInView, value])\n\n  useEffect(() => {\n    const unsubscribe = springValue.on('change', (latest) => {\n      setDisplayValue(latest)\n    })\n    return unsubscribe\n  }, [springValue])\n\n  return (\n    <span ref={ref} className={props.className}>\n      {props.prefix || ''}{formatLargeNumber(Math.floor(displayValue))}{props.suffix || ''}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAce,SAAS,gBAAgB,EACtC,KAAK,EACL,WAAW,CAAC,EACZ,SAAS,EAAE,EACX,SAAS,EAAE,EACX,WAAW,CAAC,EACZ,YAAY,EAAE,EACO;IACrB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAmB;IACpC,MAAM,cAAc,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IACnC,MAAM,cAAc,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,aAAa;QAAE,UAAU,WAAW;IAAK;IACvE,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,YAAY,GAAG,CAAC;QAClB;IACF,GAAG;QAAC;QAAa;QAAU;KAAM;IAEjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,YAAY,EAAE,CAAC,UAAU,CAAC;YAC5C,gBAAgB;QAClB;QACA,OAAO;IACT,GAAG;QAAC;KAAY;IAEhB,MAAM,eAAe,CAAC;QACpB,IAAI,WAAW,GAAG;YAChB,OAAO,IAAI,OAAO,CAAC;QACrB;QACA,OAAO,KAAK,KAAK,CAAC,KAAK,cAAc;IACvC;IAEA,qBACE,8OAAC;QAAK,KAAK;QAAK,WAAW;;YACxB;YAAQ,aAAa;YAAe;;;;;;;AAG3C;AAGO,SAAS,kBAAkB,EAAE,KAAK,EAAE,GAAG,OAA6C;IACzF,qBAAO,8OAAC;QAAgB,OAAO;QAAO,QAAO;QAAK,GAAG,KAAK;;;;;;AAC5D;AAEO,SAAS,gBAAgB,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,GAAG,OAAqE;IAC/H,qBAAO,8OAAC;QAAgB,OAAO;QAAO,QAAQ;QAAW,GAAG,KAAK;;;;;;AACnE;AAEO,SAAS,mBAAmB,EAAE,KAAK,EAAE,GAAG,OAA6B;IAC1E,MAAM,oBAAoB,CAAC;QACzB,IAAI,OAAO,SAAS;YAClB,OAAO,GAAG,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACzC;QACA,IAAI,OAAO,MAAM;YACf,OAAO,GAAG,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACtC;QACA,OAAO,IAAI,QAAQ;IACrB;IAEA,oDAAoD;IACpD,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAmB;IACpC,MAAM,cAAc,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IACnC,MAAM,cAAc,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,aAAa;QAAE,UAAU,CAAC,MAAM,QAAQ,IAAI,CAAC,IAAI;IAAK;IACpF,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,YAAY,GAAG,CAAC;QAClB;IACF,GAAG;QAAC;QAAa;QAAU;KAAM;IAEjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,YAAY,EAAE,CAAC,UAAU,CAAC;YAC5C,gBAAgB;QAClB;QACA,OAAO;IACT,GAAG;QAAC;KAAY;IAEhB,qBACE,8OAAC;QAAK,KAAK;QAAK,WAAW,MAAM,SAAS;;YACvC,MAAM,MAAM,IAAI;YAAI,kBAAkB,KAAK,KAAK,CAAC;YAAgB,MAAM,MAAM,IAAI;;;;;;;AAGxF", "debugId": null}}, {"offset": {"line": 1618, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/GlobalStatsSection.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { \n  UsersIcon, \n  GlobeIcon, \n  TargetIcon, \n  HeartIcon, \n  TrendingUpIcon,\n  StarIcon,\n  ShieldCheckIcon,\n  ZapIcon,\n  AwardIcon,\n  ClockIcon\n} from 'lucide-react'\nimport AnimatedCounter, { PercentageCounter, LargeNumberCounter } from './AnimatedCounter'\nimport { Card } from './Card'\nimport { Badge } from './Badge'\n\nconst mainStats = [\n  { \n    label: 'Active Users Worldwide', \n    value: 2500000, \n    icon: UsersIcon,\n    color: 'from-blue-500 to-blue-600',\n    description: 'Growing daily'\n  },\n  { \n    label: 'Countries Supported', \n    value: 150, \n    icon: GlobeIcon,\n    color: 'from-green-500 to-emerald-600',\n    description: 'Global reach'\n  },\n  { \n    label: 'Tasks Completed Daily', \n    value: 50000, \n    icon: TargetIcon,\n    color: 'from-purple-500 to-purple-600',\n    description: 'Productivity boost'\n  },\n  { \n    label: 'Customer Satisfaction', \n    value: 98, \n    icon: HeartIcon,\n    color: 'from-pink-500 to-red-500',\n    description: 'Happy users',\n    isPercentage: true\n  },\n]\n\nconst additionalStats = [\n  { label: 'App Store Rating', value: 4.9, icon: StarIcon, suffix: '/5' },\n  { label: 'Uptime Guarantee', value: 99.9, icon: ShieldCheckIcon, suffix: '%' },\n  { label: 'Response Time', value: 2, icon: ZapIcon, suffix: 's' },\n  { label: 'Years in Business', value: 5, icon: ClockIcon, suffix: '+' },\n]\n\nconst achievements = [\n  { title: 'Best Productivity App 2024', organization: 'TechCrunch' },\n  { title: 'Editor\\'s Choice', organization: 'Google Play' },\n  { title: 'App of the Year', organization: 'Apple App Store' },\n  { title: 'Innovation Award', organization: 'Product Hunt' },\n]\n\nconst trustedBy = [\n  'Microsoft', 'Google', 'Apple', 'Amazon', 'Meta', 'Netflix', 'Spotify', 'Uber'\n]\n\nexport default function GlobalStatsSection() {\n  return (\n    <div className=\"w-full\">\n      {/* Main Stats Grid */}\n      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 mb-16\">\n        {mainStats.map((stat, index) => (\n          <motion.div\n            key={stat.label}\n            initial={{ opacity: 0, scale: 0.5 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.6, delay: index * 0.1 }}\n            viewport={{ once: true }}\n            className=\"text-center group\"\n          >\n            <motion.div\n              className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r ${stat.color} rounded-full mb-4 group-hover:scale-110 transition-transform shadow-lg`}\n              whileHover={{ rotate: 360 }}\n              transition={{ duration: 0.6 }}\n            >\n              <stat.icon className=\"h-8 w-8 text-white\" />\n            </motion.div>\n            \n            <motion.div\n              className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-2\"\n              initial={{ opacity: 0 }}\n              whileInView={{ opacity: 1 }}\n              transition={{ duration: 1, delay: index * 0.1 + 0.5 }}\n              viewport={{ once: true }}\n            >\n              {stat.isPercentage ? (\n                <PercentageCounter value={stat.value} duration={2.5} />\n              ) : (\n                <LargeNumberCounter value={stat.value} duration={2.5} suffix=\"+\" />\n              )}\n            </motion.div>\n            \n            <div className=\"text-gray-600 font-medium mb-1\">{stat.label}</div>\n            <div className=\"text-sm text-gray-500\">{stat.description}</div>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Additional Stats Row */}\n      <motion.div\n        initial={{ opacity: 0, y: 30 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, delay: 0.3 }}\n        viewport={{ once: true }}\n        className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-16\"\n      >\n        {additionalStats.map((stat, index) => (\n          <Card key={stat.label} className=\"p-4 text-center hover:shadow-lg transition-all duration-300\">\n            <stat.icon className=\"h-6 w-6 text-gray-600 mx-auto mb-2\" />\n            <div className=\"text-2xl font-bold text-gray-900 mb-1\">\n              <AnimatedCounter \n                value={stat.value} \n                suffix={stat.suffix}\n                decimals={stat.suffix === '/5' ? 1 : 0}\n                duration={2}\n              />\n            </div>\n            <div className=\"text-sm text-gray-600\">{stat.label}</div>\n          </Card>\n        ))}\n      </motion.div>\n\n      {/* Awards & Recognition */}\n      <motion.div\n        initial={{ opacity: 0, y: 30 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, delay: 0.5 }}\n        viewport={{ once: true }}\n        className=\"mb-16\"\n      >\n        <div className=\"text-center mb-8\">\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">Awards & Recognition</h3>\n          <p className=\"text-gray-600\">Recognized by industry leaders worldwide</p>\n        </div>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          {achievements.map((achievement, index) => (\n            <motion.div\n              key={achievement.title}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              whileHover={{ scale: 1.05 }}\n            >\n              <Card className=\"p-4 text-center h-full hover:shadow-lg transition-all duration-300\">\n                <AwardIcon className=\"h-8 w-8 text-yellow-500 mx-auto mb-3\" />\n                <h4 className=\"font-semibold text-gray-900 mb-1 text-sm\">{achievement.title}</h4>\n                <p className=\"text-xs text-gray-600\">{achievement.organization}</p>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n      </motion.div>\n\n      {/* Trusted By Section */}\n      <motion.div\n        initial={{ opacity: 0, y: 30 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, delay: 0.7 }}\n        viewport={{ once: true }}\n        className=\"text-center\"\n      >\n        <div className=\"mb-8\">\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">Trusted by Industry Leaders</h3>\n          <p className=\"text-gray-600\">Join thousands of companies that rely on LifeManager</p>\n        </div>\n        \n        <div className=\"flex flex-wrap justify-center items-center gap-6 opacity-60\">\n          {trustedBy.map((company, index) => (\n            <motion.div\n              key={company}\n              initial={{ opacity: 0, scale: 0.8 }}\n              whileInView={{ opacity: 0.6, scale: 1 }}\n              transition={{ duration: 0.5, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              whileHover={{ opacity: 1, scale: 1.1 }}\n              className=\"text-lg font-semibold text-gray-700 hover:text-gray-900 transition-all duration-200 cursor-pointer\"\n            >\n              {company}\n            </motion.div>\n          ))}\n        </div>\n      </motion.div>\n\n      {/* Live Activity Indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        whileInView={{ opacity: 1 }}\n        transition={{ duration: 0.8, delay: 1 }}\n        viewport={{ once: true }}\n        className=\"mt-12 text-center\"\n      >\n        <div className=\"inline-flex items-center space-x-2 bg-green-50 text-green-700 px-4 py-2 rounded-full\">\n          <motion.div\n            className=\"w-2 h-2 bg-green-500 rounded-full\"\n            animate={{ scale: [1, 1.2, 1] }}\n            transition={{ duration: 2, repeat: Infinity }}\n          />\n          <span className=\"text-sm font-medium\">\n            <AnimatedCounter value={127} duration={3} /> users active right now\n          </span>\n        </div>\n      </motion.div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AAhBA;;;;;;AAmBA,MAAM,YAAY;IAChB;QACE,OAAO;QACP,OAAO;QACP,MAAM,wMAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM,wMAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM,0MAAA,CAAA,aAAU;QAChB,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM,wMAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;QACb,cAAc;IAChB;CACD;AAED,MAAM,kBAAkB;IACtB;QAAE,OAAO;QAAoB,OAAO;QAAK,MAAM,sMAAA,CAAA,WAAQ;QAAE,QAAQ;IAAK;IACtE;QAAE,OAAO;QAAoB,OAAO;QAAM,MAAM,wNAAA,CAAA,kBAAe;QAAE,QAAQ;IAAI;IAC7E;QAAE,OAAO;QAAiB,OAAO;QAAG,MAAM,oMAAA,CAAA,UAAO;QAAE,QAAQ;IAAI;IAC/D;QAAE,OAAO;QAAqB,OAAO;QAAG,MAAM,wMAAA,CAAA,YAAS;QAAE,QAAQ;IAAI;CACtE;AAED,MAAM,eAAe;IACnB;QAAE,OAAO;QAA8B,cAAc;IAAa;IAClE;QAAE,OAAO;QAAoB,cAAc;IAAc;IACzD;QAAE,OAAO;QAAmB,cAAc;IAAkB;IAC5D;QAAE,OAAO;QAAoB,cAAc;IAAe;CAC3D;AAED,MAAM,YAAY;IAChB;IAAa;IAAU;IAAS;IAAU;IAAQ;IAAW;IAAW;CACzE;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,aAAa;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBACpC,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ;wBAAI;wBAChD,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAW,CAAC,mEAAmE,EAAE,KAAK,KAAK,CAAC,uEAAuE,CAAC;gCACpK,YAAY;oCAAE,QAAQ;gCAAI;gCAC1B,YAAY;oCAAE,UAAU;gCAAI;0CAE5B,cAAA,8OAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;;;;;;0CAGvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,aAAa;oCAAE,SAAS;gCAAE;gCAC1B,YAAY;oCAAE,UAAU;oCAAG,OAAO,QAAQ,MAAM;gCAAI;gCACpD,UAAU;oCAAE,MAAM;gCAAK;0CAEtB,KAAK,YAAY,iBAChB,8OAAC,2IAAA,CAAA,oBAAiB;oCAAC,OAAO,KAAK,KAAK;oCAAE,UAAU;;;;;yDAEhD,8OAAC,2IAAA,CAAA,qBAAkB;oCAAC,OAAO,KAAK,KAAK;oCAAE,UAAU;oCAAK,QAAO;;;;;;;;;;;0CAIjE,8OAAC;gCAAI,WAAU;0CAAkC,KAAK,KAAK;;;;;;0CAC3D,8OAAC;gCAAI,WAAU;0CAAyB,KAAK,WAAW;;;;;;;uBA9BnD,KAAK,KAAK;;;;;;;;;;0BAoCrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,UAAU;oBAAE,MAAM;gBAAK;gBACvB,WAAU;0BAET,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC,gIAAA,CAAA,OAAI;wBAAkB,WAAU;;0CAC/B,8OAAC,KAAK,IAAI;gCAAC,WAAU;;;;;;0CACrB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,2IAAA,CAAA,UAAe;oCACd,OAAO,KAAK,KAAK;oCACjB,QAAQ,KAAK,MAAM;oCACnB,UAAU,KAAK,MAAM,KAAK,OAAO,IAAI;oCACrC,UAAU;;;;;;;;;;;0CAGd,8OAAC;gCAAI,WAAU;0CAAyB,KAAK,KAAK;;;;;;;uBAVzC,KAAK,KAAK;;;;;;;;;;0BAgBzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,UAAU;oBAAE,MAAM;gBAAK;gBACvB,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAG/B,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,OAAO;gCAAK;0CAE1B,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,wMAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAG,WAAU;sDAA4C,YAAY,KAAK;;;;;;sDAC3E,8OAAC;4CAAE,WAAU;sDAAyB,YAAY,YAAY;;;;;;;;;;;;+BAV3D,YAAY,KAAK;;;;;;;;;;;;;;;;0BAkB9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,UAAU;oBAAE,MAAM;gBAAK;gBACvB,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAG/B,8OAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,SAAS,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAK,OAAO;gCAAE;gCACtC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCACrC,WAAU;0CAET;+BARI;;;;;;;;;;;;;;;;0BAeb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,aAAa;oBAAE,SAAS;gBAAE;gBAC1B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAE;gBACtC,UAAU;oBAAE,MAAM;gBAAK;gBACvB,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,OAAO;oCAAC;oCAAG;oCAAK;iCAAE;4BAAC;4BAC9B,YAAY;gCAAE,UAAU;gCAAG,QAAQ;4BAAS;;;;;;sCAE9C,8OAAC;4BAAK,WAAU;;8CACd,8OAAC,2IAAA,CAAA,UAAe;oCAAC,OAAO;oCAAK,UAAU;;;;;;gCAAK;;;;;;;;;;;;;;;;;;;;;;;;AAMxD", "debugId": null}}, {"offset": {"line": 2168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/i18n/config.ts"], "sourcesContent": ["import { createSharedPathnamesNavigation } from 'next-intl/navigation'\n\nexport const locales = ['en', 'es', 'fr', 'de', 'ja', 'zh'] as const\nexport type Locale = (typeof locales)[number]\n\nexport const defaultLocale: Locale = 'en'\n\nexport const localeNames: Record<Locale, string> = {\n  en: 'English',\n  es: 'Español',\n  fr: 'Français', \n  de: 'Deutsch',\n  ja: '日本語',\n  zh: '中文'\n}\n\nexport const localeFlags: Record<Locale, string> = {\n  en: '🇺🇸',\n  es: '🇪🇸',\n  fr: '🇫🇷',\n  de: '🇩🇪', \n  ja: '🇯🇵',\n  zh: '🇨🇳'\n}\n\nexport const { Link, redirect, usePathname, useRouter } = createSharedPathnamesNavigation({\n  locales,\n  pathnames: {\n    '/': '/',\n    '/about': {\n      en: '/about',\n      es: '/acerca-de',\n      fr: '/a-propos',\n      de: '/uber-uns',\n      ja: '/about',\n      zh: '/about'\n    },\n    '/features': {\n      en: '/features',\n      es: '/caracteristicas',\n      fr: '/fonctionnalites',\n      de: '/funktionen',\n      ja: '/features',\n      zh: '/features'\n    },\n    '/pricing': {\n      en: '/pricing',\n      es: '/precios',\n      fr: '/tarifs',\n      de: '/preise',\n      ja: '/pricing',\n      zh: '/pricing'\n    },\n    '/contact': {\n      en: '/contact',\n      es: '/contacto',\n      fr: '/contact',\n      de: '/kontakt',\n      ja: '/contact',\n      zh: '/contact'\n    },\n    '/privacy': {\n      en: '/privacy',\n      es: '/privacidad',\n      fr: '/confidentialite',\n      de: '/datenschutz',\n      ja: '/privacy',\n      zh: '/privacy'\n    },\n    '/terms': {\n      en: '/terms',\n      es: '/terminos',\n      fr: '/conditions',\n      de: '/bedingungen',\n      ja: '/terms',\n      zh: '/terms'\n    }\n  }\n})\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAEO,MAAM,UAAU;IAAC;IAAM;IAAM;IAAM;IAAM;IAAM;CAAK;AAGpD,MAAM,gBAAwB;AAE9B,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4MAAA,CAAA,kCAA+B,AAAD,EAAE;IACxF;IACA,WAAW;QACT,KAAK;QACL,UAAU;YACR,IAAI;YACJ,IAAI;YACJ,IAA<PERSON>;Y<PERSON><PERSON>,IAAI;YAC<PERSON>,IAAI;YACJ,IAA<PERSON>;QACN;QACA,aAAa;YACX,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,YAAY;YACV,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,YAAY;YACV,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,YAAY;YACV,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;YACR,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;AACF", "debugId": null}}, {"offset": {"line": 2265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/LanguageSelector.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { GlobeIcon, ChevronDownIcon, CheckIcon } from 'lucide-react'\nimport { useLocale } from 'next-intl'\nimport { useRouter, usePathname } from 'next/navigation'\nimport { locales, localeNames, localeFlags, type Locale } from '@/i18n/config'\n\ninterface LanguageSelectorProps {\n  variant?: 'default' | 'compact'\n  className?: string\n}\n\nexport default function LanguageSelector({ \n  variant = 'default', \n  className = '' \n}: LanguageSelectorProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const locale = useLocale() as Locale\n  const router = useRouter()\n  const pathname = usePathname()\n  const dropdownRef = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  const handleLanguageChange = (newLocale: Locale) => {\n    setIsOpen(false)\n    \n    // Create new path with the new locale\n    const segments = pathname.split('/')\n    segments[1] = newLocale\n    const newPath = segments.join('/')\n    \n    router.push(newPath)\n  }\n\n  const currentLanguage = {\n    code: locale,\n    name: localeNames[locale],\n    flag: localeFlags[locale]\n  }\n\n  if (variant === 'compact') {\n    return (\n      <div className={`relative ${className}`} ref={dropdownRef}>\n        <motion.button\n          onClick={() => setIsOpen(!isOpen)}\n          className=\"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors\"\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          <span className=\"text-lg\">{currentLanguage.flag}</span>\n          <span className=\"hidden sm:inline\">{currentLanguage.code.toUpperCase()}</span>\n          <ChevronDownIcon className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />\n        </motion.button>\n\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              initial={{ opacity: 0, y: -10, scale: 0.95 }}\n              animate={{ opacity: 1, y: 0, scale: 1 }}\n              exit={{ opacity: 0, y: -10, scale: 0.95 }}\n              transition={{ duration: 0.2 }}\n              className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50\"\n            >\n              {locales.map((localeCode) => (\n                <motion.button\n                  key={localeCode}\n                  onClick={() => handleLanguageChange(localeCode)}\n                  className=\"w-full flex items-center justify-between px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n                  whileHover={{ backgroundColor: '#f3f4f6' }}\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"text-lg\">{localeFlags[localeCode]}</span>\n                    <span>{localeNames[localeCode]}</span>\n                  </div>\n                  {locale === localeCode && (\n                    <CheckIcon className=\"h-4 w-4 text-blue-600\" />\n                  )}\n                </motion.button>\n              ))}\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    )\n  }\n\n  return (\n    <div className={`relative ${className}`} ref={dropdownRef}>\n      <motion.button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-3 px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm hover:shadow-md transition-all duration-200\"\n        whileHover={{ scale: 1.02 }}\n        whileTap={{ scale: 0.98 }}\n      >\n        <GlobeIcon className=\"h-5 w-5 text-gray-600\" />\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-xl\">{currentLanguage.flag}</span>\n          <span className=\"font-medium text-gray-900\">{currentLanguage.name}</span>\n        </div>\n        <ChevronDownIcon className={`h-4 w-4 text-gray-600 transition-transform ${isOpen ? 'rotate-180' : ''}`} />\n      </motion.button>\n\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, y: -10, scale: 0.95 }}\n            animate={{ opacity: 1, y: 0, scale: 1 }}\n            exit={{ opacity: 0, y: -10, scale: 0.95 }}\n            transition={{ duration: 0.2 }}\n            className=\"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-xl border border-gray-200 py-2 z-50\"\n          >\n            <div className=\"px-4 py-2 border-b border-gray-100\">\n              <h3 className=\"text-sm font-semibold text-gray-900\">Choose Language</h3>\n              <p className=\"text-xs text-gray-500\">Select your preferred language</p>\n            </div>\n            \n            <div className=\"py-1\">\n              {locales.map((localeCode) => (\n                <motion.button\n                  key={localeCode}\n                  onClick={() => handleLanguageChange(localeCode)}\n                  className=\"w-full flex items-center justify-between px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors\"\n                  whileHover={{ backgroundColor: '#f9fafb' }}\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"text-2xl\">{localeFlags[localeCode]}</span>\n                    <div className=\"text-left\">\n                      <div className=\"font-medium\">{localeNames[localeCode]}</div>\n                      <div className=\"text-xs text-gray-500 uppercase\">{localeCode}</div>\n                    </div>\n                  </div>\n                  {locale === localeCode && (\n                    <motion.div\n                      initial={{ scale: 0 }}\n                      animate={{ scale: 1 }}\n                      className=\"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center\"\n                    >\n                      <CheckIcon className=\"h-4 w-4 text-blue-600\" />\n                    </motion.div>\n                  )}\n                </motion.button>\n              ))}\n            </div>\n            \n            <div className=\"px-4 py-2 border-t border-gray-100\">\n              <p className=\"text-xs text-gray-500\">\n                More languages coming soon!\n              </p>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  )\n}\n\n// Compact version for mobile/header use\nexport function CompactLanguageSelector(props: Omit<LanguageSelectorProps, 'variant'>) {\n  return <LanguageSelector {...props} variant=\"compact\" />\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AAce,SAAS,iBAAiB,EACvC,UAAU,SAAS,EACnB,YAAY,EAAE,EACQ;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;YACZ;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,uBAAuB,CAAC;QAC5B,UAAU;QAEV,sCAAsC;QACtC,MAAM,WAAW,SAAS,KAAK,CAAC;QAChC,QAAQ,CAAC,EAAE,GAAG;QACd,MAAM,UAAU,SAAS,IAAI,CAAC;QAE9B,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB;QACtB,MAAM;QACN,MAAM,qHAAA,CAAA,cAAW,CAAC,OAAO;QACzB,MAAM,qHAAA,CAAA,cAAW,CAAC,OAAO;IAC3B;IAEA,IAAI,YAAY,WAAW;QACzB,qBACE,8OAAC;YAAI,WAAW,CAAC,SAAS,EAAE,WAAW;YAAE,KAAK;;8BAC5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,SAAS,IAAM,UAAU,CAAC;oBAC1B,WAAU;oBACV,YAAY;wBAAE,OAAO;oBAAK;oBAC1B,UAAU;wBAAE,OAAO;oBAAK;;sCAExB,8OAAC;4BAAK,WAAU;sCAAW,gBAAgB,IAAI;;;;;;sCAC/C,8OAAC;4BAAK,WAAU;sCAAoB,gBAAgB,IAAI,CAAC,WAAW;;;;;;sCACpE,8OAAC,wNAAA,CAAA,kBAAe;4BAAC,WAAW,CAAC,6BAA6B,EAAE,SAAS,eAAe,IAAI;;;;;;;;;;;;8BAG1F,8OAAC,yLAAA,CAAA,kBAAe;8BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;4BAAI,OAAO;wBAAK;wBAC3C,SAAS;4BAAE,SAAS;4BAAG,GAAG;4BAAG,OAAO;wBAAE;wBACtC,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;4BAAI,OAAO;wBAAK;wBACxC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAET,qHAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,2BACZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,SAAS,IAAM,qBAAqB;gCACpC,WAAU;gCACV,YAAY;oCAAE,iBAAiB;gCAAU;;kDAEzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAW,qHAAA,CAAA,cAAW,CAAC,WAAW;;;;;;0DAClD,8OAAC;0DAAM,qHAAA,CAAA,cAAW,CAAC,WAAW;;;;;;;;;;;;oCAE/B,WAAW,4BACV,8OAAC,wMAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;+BAVlB;;;;;;;;;;;;;;;;;;;;;IAmBrB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;QAAE,KAAK;;0BAC5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;;kCAExB,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAW,gBAAgB,IAAI;;;;;;0CAC/C,8OAAC;gCAAK,WAAU;0CAA6B,gBAAgB,IAAI;;;;;;;;;;;;kCAEnE,8OAAC,wNAAA,CAAA,kBAAe;wBAAC,WAAW,CAAC,2CAA2C,EAAE,SAAS,eAAe,IAAI;;;;;;;;;;;;0BAGxG,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;wBAAI,OAAO;oBAAK;oBAC3C,SAAS;wBAAE,SAAS;wBAAG,GAAG;wBAAG,OAAO;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;wBAAI,OAAO;oBAAK;oBACxC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,8OAAC;4BAAI,WAAU;sCACZ,qHAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,2BACZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS,IAAM,qBAAqB;oCACpC,WAAU;oCACV,YAAY;wCAAE,iBAAiB;oCAAU;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAY,qHAAA,CAAA,cAAW,CAAC,WAAW;;;;;;8DACnD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAe,qHAAA,CAAA,cAAW,CAAC,WAAW;;;;;;sEACrD,8OAAC;4DAAI,WAAU;sEAAmC;;;;;;;;;;;;;;;;;;wCAGrD,WAAW,4BACV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO;4CAAE;4CACpB,WAAU;sDAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;mCAlBpB;;;;;;;;;;sCAyBX,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD;AAGO,SAAS,wBAAwB,KAA6C;IACnF,qBAAO,8OAAC;QAAkB,GAAG,KAAK;QAAE,SAAQ;;;;;;AAC9C", "debugId": null}}, {"offset": {"line": 2678, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useState, useEffect, useCallback } from 'react'\nimport { createClient } from '@/lib/supabase/client'\nimport { redirect } from 'next/navigation'\nimport { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion'\nimport { <PERSON><PERSON> } from '@/components/ui/Button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Badge } from '@/components/ui/Badge'\nimport TestimonialsCarousel from '@/components/ui/TestimonialsCarousel'\nimport PricingSection from '@/components/ui/PricingSection'\nimport GlobalStatsSection from '@/components/ui/GlobalStatsSection'\nimport { CompactLanguageSelector } from '@/components/ui/LanguageSelector'\nimport {\n  CheckSquareIcon,\n  DollarSignIcon,\n  ShoppingCartIcon,\n  MessageCircleIcon,\n  ChefHatIcon,\n  ArrowRightIcon,\n  StarIcon,\n  GlobeIcon,\n  ShieldCheckIcon,\n  ZapIcon,\n  TrendingUpIcon,\n  UsersIcon,\n  CheckIcon,\n  PlayIcon,\n  SmartphoneIcon,\n  LaptopIcon,\n  TabletIcon,\n  SparklesIcon,\n  RocketIcon,\n  HeartIcon,\n  BrainIcon,\n  ClockIcon,\n  TargetIcon,\n  ChevronDownIcon,\n  MouseIcon\n} from 'lucide-react'\n\n// Typing Animation Component\nconst TypingAnimation = ({ words, className = \"\" }: { words: string[], className?: string }) => {\n  const [currentWordIndex, setCurrentWordIndex] = useState(0)\n  const [currentText, setCurrentText] = useState('')\n  const [isDeleting, setIsDeleting] = useState(false)\n\n  useEffect(() => {\n    const word = words[currentWordIndex]\n    const timeout = setTimeout(() => {\n      if (!isDeleting) {\n        if (currentText.length < word.length) {\n          setCurrentText(word.slice(0, currentText.length + 1))\n        } else {\n          setTimeout(() => setIsDeleting(true), 2000)\n        }\n      } else {\n        if (currentText.length > 0) {\n          setCurrentText(currentText.slice(0, -1))\n        } else {\n          setIsDeleting(false)\n          setCurrentWordIndex((prev) => (prev + 1) % words.length)\n        }\n      }\n    }, isDeleting ? 50 : 100)\n\n    return () => clearTimeout(timeout)\n  }, [currentText, isDeleting, currentWordIndex, words])\n\n  return (\n    <span className={className}>\n      {currentText}\n      <motion.span\n        animate={{ opacity: [1, 0] }}\n        transition={{ duration: 0.8, repeat: Infinity, repeatType: \"reverse\" }}\n        className=\"inline-block w-0.5 h-8 bg-blue-600 ml-1\"\n      />\n    </span>\n  )\n}\n\n// Floating Elements Component\nconst FloatingElements = () => {\n  const floatingIcons = [\n    { Icon: CheckSquareIcon, delay: 0, x: \"10%\", y: \"20%\" },\n    { Icon: DollarSignIcon, delay: 0.5, x: \"85%\", y: \"15%\" },\n    { Icon: ShoppingCartIcon, delay: 1, x: \"15%\", y: \"70%\" },\n    { Icon: BrainIcon, delay: 1.5, x: \"80%\", y: \"75%\" },\n    { Icon: ChefHatIcon, delay: 2, x: \"50%\", y: \"10%\" },\n    { Icon: ShieldCheckIcon, delay: 2.5, x: \"90%\", y: \"45%\" }\n  ]\n\n  return (\n    <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n      {floatingIcons.map(({ Icon, delay, x, y }, index) => (\n        <motion.div\n          key={index}\n          className=\"absolute\"\n          style={{ left: x, top: y }}\n          initial={{ opacity: 0, scale: 0 }}\n          animate={{\n            opacity: [0, 0.6, 0],\n            scale: [0, 1, 0],\n            y: [0, -20, 0]\n          }}\n          transition={{\n            duration: 4,\n            delay,\n            repeat: Infinity,\n            repeatType: \"loop\"\n          }}\n        >\n          <Icon className=\"h-8 w-8 text-blue-400\" />\n        </motion.div>\n      ))}\n    </div>\n  )\n}\n\n// Scroll Indicator Component\nconst ScrollIndicator = () => {\n  return (\n    <motion.div\n      className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center text-gray-600\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ delay: 2, duration: 0.8 }}\n    >\n      <motion.div\n        animate={{ y: [0, 10, 0] }}\n        transition={{ duration: 2, repeat: Infinity, repeatType: \"loop\" }}\n        className=\"flex flex-col items-center\"\n      >\n        <span className=\"text-sm mb-2\">Scroll to explore</span>\n        <ChevronDownIcon className=\"h-5 w-5\" />\n      </motion.div>\n    </motion.div>\n  )\n}\n\nexport default function Home() {\n  const [user, setUser] = useState(null)\n  const [loading, setLoading] = useState(true)\n  const supabase = createClient()\n  const { scrollY } = useScroll()\n  const heroY = useTransform(scrollY, [0, 500], [0, -150])\n  const heroOpacity = useTransform(scrollY, [0, 300], [1, 0])\n\n  useEffect(() => {\n    const checkUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (user) {\n        window.location.href = '/dashboard'\n      }\n      setUser(user)\n      setLoading(false)\n    }\n    checkUser()\n  }, [])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 flex items-center justify-center\">\n        <motion.div\n          initial={{ opacity: 0, scale: 0.5 }}\n          animate={{ opacity: 1, scale: 1 }}\n          className=\"text-white text-center\"\n        >\n          <SparklesIcon className=\"h-16 w-16 mx-auto mb-4 animate-pulse\" />\n          <p className=\"text-xl\">Loading LifeManager...</p>\n        </motion.div>\n      </div>\n    )\n  }\n\n  const features = [\n    {\n      name: 'Smart Task Management',\n      description: 'AI-powered task organization with drag-and-drop, priorities, and intelligent scheduling.',\n      icon: CheckSquareIcon,\n      color: 'from-blue-500 to-blue-600',\n      stats: '10M+ tasks completed',\n    },\n    {\n      name: 'Advanced Budget Tracking',\n      description: 'Real-time expense monitoring with predictive analytics and automated categorization.',\n      icon: DollarSignIcon,\n      color: 'from-green-500 to-emerald-600',\n      stats: '$2B+ tracked globally',\n    },\n    {\n      name: 'Intelligent Shopping',\n      description: 'Smart shopping lists with price tracking, store optimization, and family sharing.',\n      icon: ShoppingCartIcon,\n      color: 'from-purple-500 to-purple-600',\n      stats: '500K+ lists created',\n    },\n    {\n      name: 'AI Life Assistant',\n      description: 'Personalized recommendations and insights powered by advanced machine learning.',\n      icon: BrainIcon,\n      color: 'from-indigo-500 to-indigo-600',\n      stats: '1M+ decisions optimized',\n    },\n    {\n      name: 'Recipe Intelligence',\n      description: 'Auto-import recipes, nutritional analysis, and meal planning with dietary preferences.',\n      icon: ChefHatIcon,\n      color: 'from-orange-500 to-red-500',\n      stats: '100K+ recipes saved',\n    },\n    {\n      name: 'Global Sync & Security',\n      description: 'End-to-end encryption with real-time sync across all your devices worldwide.',\n      icon: ShieldCheckIcon,\n      color: 'from-teal-500 to-cyan-600',\n      stats: '99.9% uptime guarantee',\n    },\n  ]\n\n  const testimonials = [\n    {\n      name: 'Sarah Chen',\n      role: 'Product Manager',\n      location: 'San Francisco, USA',\n      image: '/api/placeholder/64/64',\n      content: 'LifeManager transformed how I organize my life. The AI suggestions are incredibly accurate and have saved me hours every week.',\n      rating: 5,\n    },\n    {\n      name: 'Marcus Johnson',\n      role: 'Entrepreneur',\n      location: 'London, UK',\n      image: '/api/placeholder/64/64',\n      content: 'As someone juggling multiple businesses, LifeManager keeps me on track. The budget tracking alone has saved me thousands.',\n      rating: 5,\n    },\n    {\n      name: 'Yuki Tanaka',\n      role: 'Software Engineer',\n      location: 'Tokyo, Japan',\n      image: '/api/placeholder/64/64',\n      content: 'The cross-platform sync is flawless. I can start a task on my phone and finish it on my laptop seamlessly.',\n      rating: 5,\n    },\n    {\n      name: 'Emma Rodriguez',\n      role: 'Marketing Director',\n      location: 'Barcelona, Spain',\n      image: '/api/placeholder/64/64',\n      content: 'The recipe management feature is a game-changer! I can import recipes from any website and plan my meals effortlessly.',\n      rating: 5,\n    },\n    {\n      name: 'David Kim',\n      role: 'Freelance Designer',\n      location: 'Seoul, South Korea',\n      image: '/api/placeholder/64/64',\n      content: 'I love how everything is connected. My shopping lists sync with my budget, and the AI chat helps me make better decisions.',\n      rating: 5,\n    },\n    {\n      name: 'Lisa Thompson',\n      role: 'Working Mom',\n      location: 'Toronto, Canada',\n      image: '/api/placeholder/64/64',\n      content: 'Managing a family and career is tough, but LifeManager makes it so much easier. The mobile app is perfect for on-the-go planning.',\n      rating: 5,\n    },\n  ]\n\n\n\n\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <header className=\"bg-white/95 backdrop-blur-sm shadow-sm sticky top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div className=\"flex items-center\">\n              <motion.div\n                whileHover={{ rotate: 360 }}\n                transition={{ duration: 0.6 }}\n                className=\"bg-gradient-to-r from-blue-500 to-purple-600 p-2 rounded-full mr-3\"\n              >\n                <RocketIcon className=\"h-6 w-6 text-white\" />\n              </motion.div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">LifeManager</h1>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              <nav className=\"hidden md:flex items-center space-x-6\">\n                <Link href=\"/about\" className=\"text-gray-600 hover:text-gray-900 font-medium transition-colors\">\n                  About\n                </Link>\n                <Link href=\"/features\" className=\"text-gray-600 hover:text-gray-900 font-medium transition-colors\">\n                  Features\n                </Link>\n                <Link href=\"/pricing\" className=\"text-gray-600 hover:text-gray-900 font-medium transition-colors\">\n                  Pricing\n                </Link>\n                <Link href=\"/contact\" className=\"text-gray-600 hover:text-gray-900 font-medium transition-colors\">\n                  Contact\n                </Link>\n              </nav>\n\n              <CompactLanguageSelector />\n\n              <div className=\"flex items-center space-x-3\">\n                <Button variant=\"outline\" size=\"sm\">\n                  <Link href=\"/login\">Sign In</Link>\n                </Button>\n                <Button size=\"sm\">\n                  <Link href=\"/signup\">Get Started</Link>\n                </Button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <motion.section\n        className=\"relative py-20 min-h-screen flex items-center overflow-hidden\"\n        style={{ y: heroY, opacity: heroOpacity }}\n      >\n        <FloatingElements />\n\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"mb-6\"\n          >\n            <Badge variant=\"purple\" size=\"lg\" className=\"mb-4 animate-pulse\">\n              <SparklesIcon className=\"h-4 w-4 mr-2\" />\n              AI-Powered Life Management\n            </Badge>\n          </motion.div>\n\n          <motion.h1\n            className=\"text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n          >\n            Manage Your Life{' '}\n            <br className=\"hidden sm:block\" />\n            <TypingAnimation\n              words={[\"Effortlessly\", \"Intelligently\", \"Seamlessly\", \"Efficiently\"]}\n              className=\"text-blue-600\"\n            />\n          </motion.h1>\n\n          <motion.p\n            className=\"text-xl md:text-2xl text-gray-600 mb-8 max-w-4xl mx-auto leading-relaxed\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n          >\n            The all-in-one platform to organize your tasks, track your budget,\n            manage shopping lists, and get AI-powered assistance for better life management.\n          </motion.p>\n\n          <motion.div\n            className=\"flex flex-col sm:flex-row gap-4 justify-center mb-12\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n          >\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <Button size=\"xl\" className=\"group\">\n                <Link href=\"/signup\" className=\"flex items-center\">\n                  Start Free Today\n                  <motion.div\n                    className=\"ml-2\"\n                    animate={{ x: [0, 5, 0] }}\n                    transition={{ duration: 1.5, repeat: Infinity }}\n                  >\n                    <ArrowRightIcon className=\"h-5 w-5\" />\n                  </motion.div>\n                </Link>\n              </Button>\n            </motion.div>\n\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <Button variant=\"outline\" size=\"xl\">\n                <Link href=\"/login\" className=\"flex items-center\">\n                  <PlayIcon className=\"h-5 w-5 mr-2\" />\n                  Watch Demo\n                </Link>\n              </Button>\n            </motion.div>\n          </motion.div>\n\n          {/* Trust Indicators */}\n          <motion.div\n            className=\"flex flex-wrap justify-center items-center gap-8 text-gray-500\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.8, delay: 1.2 }}\n          >\n            <div className=\"flex items-center\">\n              <UsersIcon className=\"h-5 w-5 mr-2\" />\n              <span className=\"text-sm font-medium\">2.5M+ Users</span>\n            </div>\n            <div className=\"flex items-center\">\n              <StarIcon className=\"h-5 w-5 mr-2 text-yellow-400 fill-current\" />\n              <span className=\"text-sm font-medium\">4.9/5 Rating</span>\n            </div>\n            <div className=\"flex items-center\">\n              <ShieldCheckIcon className=\"h-5 w-5 mr-2 text-green-500\" />\n              <span className=\"text-sm font-medium\">Enterprise Security</span>\n            </div>\n          </motion.div>\n        </div>\n\n        <ScrollIndicator />\n      </motion.section>\n\n      {/* Features Section */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            className=\"text-center mb-16\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Everything You Need in One Place\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Streamline your daily life with our comprehensive suite of management tools.\n            </p>\n          </motion.div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {features.map((feature, index) => (\n              <motion.div\n                key={feature.name}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                whileHover={{\n                  scale: 1.05,\n                  boxShadow: \"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)\"\n                }}\n                className=\"group\"\n              >\n                <Card className=\"h-full p-6 cursor-pointer border-2 border-transparent hover:border-blue-200 transition-all duration-300\">\n                  <div className=\"flex items-center mb-4\">\n                    <motion.div\n                      className={`p-3 rounded-xl bg-gradient-to-r ${feature.color} text-white`}\n                      whileHover={{ rotate: 360 }}\n                      transition={{ duration: 0.6 }}\n                    >\n                      <feature.icon className=\"h-6 w-6\" />\n                    </motion.div>\n                    <h3 className=\"text-xl font-semibold text-gray-900 ml-4 group-hover:text-blue-600 transition-colors\">\n                      {feature.name}\n                    </h3>\n                  </div>\n                  <p className=\"text-gray-600 mb-4 leading-relaxed\">{feature.description}</p>\n                  <div className=\"flex items-center justify-between\">\n                    <Badge variant=\"secondary\" className=\"text-xs\">\n                      {feature.stats}\n                    </Badge>\n                    <motion.div\n                      className=\"opacity-0 group-hover:opacity-100 transition-opacity\"\n                      whileHover={{ x: 5 }}\n                    >\n                      <ArrowRightIcon className=\"h-4 w-4 text-blue-600\" />\n                    </motion.div>\n                  </div>\n                </Card>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Testimonials Carousel */}\n      <section className=\"py-20 bg-gradient-to-br from-blue-50 to-indigo-100\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            className=\"text-center mb-16\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Loved by Users Worldwide\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              See what our community has to say about transforming their daily lives.\n            </p>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            <TestimonialsCarousel\n              testimonials={testimonials}\n              autoPlay={true}\n              autoPlayInterval={6000}\n              showControls={true}\n              showIndicators={true}\n            />\n          </motion.div>\n\n          {/* Additional testimonials grid for more social proof */}\n          <motion.div\n            className=\"mt-16 grid grid-cols-1 md:grid-cols-3 gap-6\"\n            initial={{ opacity: 0 }}\n            whileInView={{ opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            viewport={{ once: true }}\n          >\n            {testimonials.slice(0, 3).map((testimonial, index) => (\n              <motion.div\n                key={`grid-${testimonial.name}`}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                whileHover={{ scale: 1.02 }}\n                className=\"group\"\n              >\n                <Card className=\"p-4 text-center hover:shadow-lg transition-all duration-300 bg-white/70 backdrop-blur-sm\">\n                  <div className=\"flex justify-center mb-3\">\n                    {[...Array(testimonial.rating)].map((_, i) => (\n                      <StarIcon key={i} className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                    ))}\n                  </div>\n                  <blockquote className=\"text-gray-700 text-sm mb-4 italic\">\n                    \"{testimonial.content.slice(0, 100)}...\"\n                  </blockquote>\n                  <div className=\"flex items-center justify-center\">\n                    <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xs mr-3\">\n                      {testimonial.name.split(' ').map(n => n[0]).join('')}\n                    </div>\n                    <div className=\"text-left\">\n                      <div className=\"font-semibold text-gray-900 text-sm\">{testimonial.name}</div>\n                      <div className=\"text-xs text-gray-600\">{testimonial.role}</div>\n                    </div>\n                  </div>\n                </Card>\n              </motion.div>\n            ))}\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Global Stats Section */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            className=\"text-center mb-16\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Trusted Globally\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Join millions of users who have transformed their productivity and life management.\n            </p>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            <GlobalStatsSection />\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Pricing Section */}\n      <section className=\"py-20 bg-gradient-to-br from-gray-50 to-blue-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            className=\"text-center mb-16\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Choose Your Plan\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Start free and upgrade as you grow. All plans include our core features.\n            </p>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            <PricingSection />\n          </motion.div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 bg-gradient-to-r from-blue-600 to-purple-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-4\">\n              Ready to Take Control?\n            </h2>\n            <p className=\"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\">\n              Join thousands of users who have transformed their daily routines with LifeManager.\n            </p>\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <Button size=\"xl\" variant=\"secondary\" className=\"bg-white text-blue-600 hover:bg-gray-100\">\n                <Link href=\"/signup\" className=\"flex items-center\">\n                  Get Started Free\n                  <motion.div\n                    className=\"ml-2\"\n                    animate={{ x: [0, 5, 0] }}\n                    transition={{ duration: 1.5, repeat: Infinity }}\n                  >\n                    <ArrowRightIcon className=\"h-5 w-5\" />\n                  </motion.div>\n                </Link>\n              </Button>\n            </motion.div>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            className=\"text-center\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"flex items-center justify-center mb-6\">\n              <motion.div\n                whileHover={{ rotate: 360 }}\n                transition={{ duration: 0.6 }}\n                className=\"bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-full mr-4\"\n              >\n                <RocketIcon className=\"h-8 w-8 text-white\" />\n              </motion.div>\n              <h3 className=\"text-3xl font-bold\">LifeManager</h3>\n            </div>\n            <p className=\"text-gray-400 mb-8 text-lg max-w-2xl mx-auto\">\n              Simplifying life management, one task at a time. Join the revolution of organized living.\n            </p>\n\n            <div className=\"flex flex-wrap justify-center gap-8 mb-8 text-sm\">\n              <Link href=\"/about\" className=\"text-gray-400 hover:text-white transition-colors\">About</Link>\n              <Link href=\"/features\" className=\"text-gray-400 hover:text-white transition-colors\">Features</Link>\n              <Link href=\"/pricing\" className=\"text-gray-400 hover:text-white transition-colors\">Pricing</Link>\n              <Link href=\"/contact\" className=\"text-gray-400 hover:text-white transition-colors\">Contact</Link>\n              <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white transition-colors\">Privacy</Link>\n              <Link href=\"/terms\" className=\"text-gray-400 hover:text-white transition-colors\">Terms</Link>\n            </div>\n\n            <div className=\"border-t border-gray-800 pt-8\">\n              <p className=\"text-gray-500 text-sm\">\n                © 2024 LifeManager. All rights reserved. Made with ❤️ for productivity enthusiasts worldwide.\n              </p>\n            </div>\n          </motion.div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAdA;;;;;;;;;;;;;;AA0CA,6BAA6B;AAC7B,MAAM,kBAAkB,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,EAA2C;IACzF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,KAAK,CAAC,iBAAiB;QACpC,MAAM,UAAU,WAAW;YACzB,IAAI,CAAC,YAAY;gBACf,IAAI,YAAY,MAAM,GAAG,KAAK,MAAM,EAAE;oBACpC,eAAe,KAAK,KAAK,CAAC,GAAG,YAAY,MAAM,GAAG;gBACpD,OAAO;oBACL,WAAW,IAAM,cAAc,OAAO;gBACxC;YACF,OAAO;gBACL,IAAI,YAAY,MAAM,GAAG,GAAG;oBAC1B,eAAe,YAAY,KAAK,CAAC,GAAG,CAAC;gBACvC,OAAO;oBACL,cAAc;oBACd,oBAAoB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;gBACzD;YACF;QACF,GAAG,aAAa,KAAK;QAErB,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAa;QAAY;QAAkB;KAAM;IAErD,qBACE,8OAAC;QAAK,WAAW;;YACd;0BACD,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,SAAS;oBAAE,SAAS;wBAAC;wBAAG;qBAAE;gBAAC;gBAC3B,YAAY;oBAAE,UAAU;oBAAK,QAAQ;oBAAU,YAAY;gBAAU;gBACrE,WAAU;;;;;;;;;;;;AAIlB;AAEA,8BAA8B;AAC9B,MAAM,mBAAmB;IACvB,MAAM,gBAAgB;QACpB;YAAE,MAAM,+NAAA,CAAA,kBAAe;YAAE,OAAO;YAAG,GAAG;YAAO,GAAG;QAAM;QACtD;YAAE,MAAM,sNAAA,CAAA,iBAAc;YAAE,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QACvD;YAAE,MAAM,0NAAA,CAAA,mBAAgB;YAAE,OAAO;YAAG,GAAG;YAAO,GAAG;QAAM;QACvD;YAAE,MAAM,wMAAA,CAAA,YAAS;YAAE,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAClD;YAAE,MAAM,gNAAA,CAAA,cAAW;YAAE,OAAO;YAAG,GAAG;YAAO,GAAG;QAAM;QAClD;YAAE,MAAM,wNAAA,CAAA,kBAAe;YAAE,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;KACzD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACZ,cAAc,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,sBACzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,WAAU;gBACV,OAAO;oBAAE,MAAM;oBAAG,KAAK;gBAAE;gBACzB,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,SAAS;oBACP,SAAS;wBAAC;wBAAG;wBAAK;qBAAE;oBACpB,OAAO;wBAAC;wBAAG;wBAAG;qBAAE;oBAChB,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;gBAChB;gBACA,YAAY;oBACV,UAAU;oBACV;oBACA,QAAQ;oBACR,YAAY;gBACd;0BAEA,cAAA,8OAAC;oBAAK,WAAU;;;;;;eAhBX;;;;;;;;;;AAqBf;AAEA,6BAA6B;AAC7B,MAAM,kBAAkB;IACtB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,OAAO;YAAG,UAAU;QAAI;kBAEtC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,GAAG;oBAAC;oBAAG;oBAAI;iBAAE;YAAC;YACzB,YAAY;gBAAE,UAAU;gBAAG,QAAQ;gBAAU,YAAY;YAAO;YAChE,WAAU;;8BAEV,8OAAC;oBAAK,WAAU;8BAAe;;;;;;8BAC/B,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEe,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD;IAC5B,MAAM,QAAQ,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,SAAS;QAAC;QAAG;KAAI,EAAE;QAAC;QAAG,CAAC;KAAI;IACvD,MAAM,cAAc,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,SAAS;QAAC;QAAG;KAAI,EAAE;QAAC;QAAG;KAAE;IAE1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,MAAM;gBACR,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;YACA,QAAQ;YACR,WAAW;QACb;QACA;IACF,GAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAI;gBAClC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,WAAU;;kCAEV,8OAAC,8MAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,8OAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;;;;;;;IAI/B;IAEA,MAAM,WAAW;QACf;YACE,MAAM;YACN,aAAa;YACb,MAAM,+NAAA,CAAA,kBAAe;YACrB,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,sNAAA,CAAA,iBAAc;YACpB,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,0NAAA,CAAA,mBAAgB;YACtB,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,wMAAA,CAAA,YAAS;YACf,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,gNAAA,CAAA,cAAW;YACjB,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,wNAAA,CAAA,kBAAe;YACrB,OAAO;YACP,OAAO;QACT;KACD;IAED,MAAM,eAAe;QACnB;YACE,MAAM;YACN,MAAM;YACN,UAAU;YACV,OAAO;YACP,SAAS;YACT,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,UAAU;YACV,OAAO;YACP,SAAS;YACT,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,UAAU;YACV,OAAO;YACP,SAAS;YACT,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,UAAU;YACV,OAAO;YACP,SAAS;YACT,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,UAAU;YACV,OAAO;YACP,SAAS;YACT,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,UAAU;YACV,OAAO;YACP,SAAS;YACT,QAAQ;QACV;KACD;IAMD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,QAAQ;wCAAI;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;;0CAGnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAkE;;;;;;0DAGhG,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAkE;;;;;;0DAGnG,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAkE;;;;;;0DAGlG,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAkE;;;;;;;;;;;;kDAKpG,8OAAC,4IAAA,CAAA,0BAAuB;;;;;kDAExB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;0DAC7B,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAS;;;;;;;;;;;0DAEtB,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;0DACX,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;gBACb,WAAU;gBACV,OAAO;oBAAE,GAAG;oBAAO,SAAS;gBAAY;;kCAExC,8OAAC;;;;;kCAED,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CAEV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAS,MAAK;oCAAK,WAAU;;sDAC1C,8OAAC,8MAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAK7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;oCACzC;oCACkB;kDACjB,8OAAC;wCAAG,WAAU;;;;;;kDACd,8OAAC;wCACC,OAAO;4CAAC;4CAAgB;4CAAiB;4CAAc;yCAAc;wCACrE,WAAU;;;;;;;;;;;;0CAId,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CACzC;;;;;;0CAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;kDAExC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAC1B,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;;oDAAoB;kEAEjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,SAAS;4DAAE,GAAG;gEAAC;gEAAG;gEAAG;6DAAE;wDAAC;wDACxB,YAAY;4DAAE,UAAU;4DAAK,QAAQ;wDAAS;kEAE9C,cAAA,8OAAC,sNAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAC7B,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;;kEAC5B,8OAAC,sMAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;0CAQ7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;kDAExC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wMAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wNAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;0DAC3B,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;;;;;;;kCAK5C,8OAAC;;;;;;;;;;;0BAIH,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCACV,OAAO;wCACP,WAAW;oCACb;oCACA,WAAU;8CAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAW,CAAC,gCAAgC,EAAE,QAAQ,KAAK,CAAC,WAAW,CAAC;wDACxE,YAAY;4DAAE,QAAQ;wDAAI;wDAC1B,YAAY;4DAAE,UAAU;wDAAI;kEAE5B,cAAA,8OAAC,QAAQ,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAE1B,8OAAC;wDAAG,WAAU;kEACX,QAAQ,IAAI;;;;;;;;;;;;0DAGjB,8OAAC;gDAAE,WAAU;0DAAsC,QAAQ,WAAW;;;;;;0DACtE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;kEAClC,QAAQ,KAAK;;;;;;kEAEhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,YAAY;4DAAE,GAAG;wDAAE;kEAEnB,cAAA,8OAAC,sNAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mCAjC3B,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;0BA4C3B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,gJAAA,CAAA,UAAoB;gCACnB,cAAc;gCACd,UAAU;gCACV,kBAAkB;gCAClB,cAAc;gCACd,gBAAgB;;;;;;;;;;;sCAKpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,aAAa;gCAAE,SAAS;4BAAE;4BAC1B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCAEtB,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,aAAa,sBAC1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,WAAU;8CAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM,YAAY,MAAM;iDAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,8OAAC,sMAAA,CAAA,WAAQ;wDAAS,WAAU;uDAAb;;;;;;;;;;0DAGnB,8OAAC;gDAAW,WAAU;;oDAAoC;oDACtD,YAAY,OAAO,CAAC,KAAK,CAAC,GAAG;oDAAK;;;;;;;0DAEtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;kEAEnD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAuC,YAAY,IAAI;;;;;;0EACtE,8OAAC;gEAAI,WAAU;0EAAyB,YAAY,IAAI;;;;;;;;;;;;;;;;;;;;;;;;mCAvBzD,CAAC,KAAK,EAAE,YAAY,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;0BAkCzC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,8IAAA,CAAA,UAAkB;;;;;;;;;;;;;;;;;;;;;0BAMzB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,0IAAA,CAAA,UAAc;;;;;;;;;;;;;;;;;;;;;0BAMrB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;;0CAEvB,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAG/D,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAG5D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAQ;oCAAY,WAAU;8CAC9C,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAU,WAAU;;4CAAoB;0DAEjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,GAAG;wDAAC;wDAAG;wDAAG;qDAAE;gDAAC;gDACxB,YAAY;oDAAE,UAAU;oDAAK,QAAQ;gDAAS;0DAE9C,cAAA,8OAAC,sNAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUxC,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;;0CAEvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,QAAQ;wCAAI;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;;;;;;;0CAErC,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAI5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAmD;;;;;;kDACjF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAmD;;;;;;kDACpF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAmD;;;;;;kDACnF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAmD;;;;;;kDACnF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAmD;;;;;;kDACnF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAmD;;;;;;;;;;;;0CAGnF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}]}