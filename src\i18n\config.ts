import { createSharedPathnamesNavigation } from 'next-intl/navigation'

export const locales = ['en', 'es', 'fr', 'de', 'ja', 'zh'] as const
export type Locale = (typeof locales)[number]

export const defaultLocale: Locale = 'en'

export const localeNames: Record<Locale, string> = {
  en: 'English',
  es: 'Español',
  fr: 'Français', 
  de: 'Deutsch',
  ja: '日本語',
  zh: '中文'
}

export const localeFlags: Record<Locale, string> = {
  en: '🇺🇸',
  es: '🇪🇸',
  fr: '🇫🇷',
  de: '🇩🇪', 
  ja: '🇯🇵',
  zh: '🇨🇳'
}

export const { Link, redirect, usePathname, useRouter } = createSharedPathnamesNavigation({
  locales,
  pathnames: {
    '/': '/',
    '/about': {
      en: '/about',
      es: '/acerca-de',
      fr: '/a-propos',
      de: '/uber-uns',
      ja: '/about',
      zh: '/about'
    },
    '/features': {
      en: '/features',
      es: '/caracteristicas',
      fr: '/fonctionnalites',
      de: '/funktionen',
      ja: '/features',
      zh: '/features'
    },
    '/pricing': {
      en: '/pricing',
      es: '/precios',
      fr: '/tarifs',
      de: '/preise',
      ja: '/pricing',
      zh: '/pricing'
    },
    '/contact': {
      en: '/contact',
      es: '/contacto',
      fr: '/contact',
      de: '/kontakt',
      ja: '/contact',
      zh: '/contact'
    },
    '/privacy': {
      en: '/privacy',
      es: '/privacidad',
      fr: '/confidentialite',
      de: '/datenschutz',
      ja: '/privacy',
      zh: '/privacy'
    },
    '/terms': {
      en: '/terms',
      es: '/terminos',
      fr: '/conditions',
      de: '/bedingungen',
      ja: '/terms',
      zh: '/terms'
    }
  }
})
