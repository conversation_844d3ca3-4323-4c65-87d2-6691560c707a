{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\nimport { cva, type VariantProps } from 'class-variance-authority'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed ring-offset-background transform hover:scale-105 active:scale-95 focus:scale-105',\n  {\n    variants: {\n      variant: {\n        default: 'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl focus-visible:ring-blue-500',\n        destructive: 'bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-lg hover:shadow-xl focus-visible:ring-red-500',\n        outline: 'border-2 border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus-visible:ring-gray-500',\n        secondary: 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-900 hover:from-gray-200 hover:to-gray-300 focus-visible:ring-gray-500',\n        ghost: 'hover:bg-gray-100 text-gray-700 focus-visible:ring-gray-500',\n        link: 'underline-offset-4 hover:underline text-blue-600 focus-visible:ring-blue-500',\n        success: 'bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl focus-visible:ring-green-500',\n        warning: 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white hover:from-yellow-600 hover:to-yellow-700 shadow-lg hover:shadow-xl focus-visible:ring-yellow-500',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 text-xs',\n        lg: 'h-12 px-8 text-base',\n        xl: 'h-14 px-10 text-lg',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean\n  loadingText?: string\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, loadingText, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        aria-disabled={disabled || loading}\n        {...props}\n      >\n        {loading ? (\n          <>\n            <svg\n              className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              aria-hidden=\"true\"\n            >\n              <circle\n                className=\"opacity-25\"\n                cx=\"12\"\n                cy=\"12\"\n                r=\"10\"\n                stroke=\"currentColor\"\n                strokeWidth=\"4\"\n              ></circle>\n              <path\n                className=\"opacity-75\"\n                fill=\"currentColor\"\n                d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n              ></path>\n            </svg>\n            {loadingText || 'Loading...'}\n          </>\n        ) : (\n          children\n        )}\n      </button>\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,gVACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACjF,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACtB,iBAAe,YAAY;QAC1B,GAAG,KAAK;kBAER,wBACC;;8BACE,8OAAC;oBACC,WAAU;oBACV,OAAM;oBACN,MAAK;oBACL,SAAQ;oBACR,eAAY;;sCAEZ,8OAAC;4BACC,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;;;;;;sCAEd,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;gBAGL,eAAe;;2BAGlB;;;;;;AAIR;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Card.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nconst Card = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border border-gray-200 bg-white text-gray-950 shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCard.displayName = 'Card'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex flex-col space-y-1.5 p-6', className)} {...props} />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('text-lg font-semibold leading-none tracking-tight text-gray-900', className)}\n      {...props}\n    />\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p ref={ref} className={cn('text-sm text-gray-600', className)} {...props} />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex items-center p-6 pt-0', className)} {...props} />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACpB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAGvF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAE,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QAAa,GAAG,KAAK;;;;;;AAG7E,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAGlE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAGpF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Input.tsx"], "sourcesContent": ["import { InputHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps extends InputHTMLAttributes<HTMLInputElement> {\n  error?: boolean\n  helperText?: string\n  label?: string\n}\n\nconst Input = forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, helperText, label, id, ...props }, ref) => {\n    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`\n    const helperTextId = helperText ? `${inputId}-helper` : undefined\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 mb-1\"\n          >\n            {label}\n            {props.required && <span className=\"text-red-500 ml-1\" aria-label=\"required\">*</span>}\n          </label>\n        )}\n        <input\n          id={inputId}\n          type={type}\n          className={cn(\n            'flex h-10 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 hover:border-gray-400',\n            error && 'border-red-500 focus-visible:ring-red-500 aria-invalid:border-red-500',\n            className\n          )}\n          ref={ref}\n          aria-invalid={error}\n          aria-describedby={helperTextId}\n          {...props}\n        />\n        {helperText && (\n          <p\n            id={helperTextId}\n            className={cn(\n              'mt-1 text-xs',\n              error ? 'text-red-600' : 'text-gray-500'\n            )}\n            role={error ? 'alert' : 'status'}\n          >\n            {helperText}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE;IAC5D,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IACxE,MAAM,eAAe,aAAa,GAAG,QAAQ,OAAO,CAAC,GAAG;IAExD,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,MAAM,QAAQ,kBAAI,8OAAC;wBAAK,WAAU;wBAAoB,cAAW;kCAAW;;;;;;;;;;;;0BAGjF,8OAAC;gBACC,IAAI;gBACJ,MAAM;gBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA,SAAS,yEACT;gBAEF,KAAK;gBACL,gBAAc;gBACd,oBAAkB;gBACjB,GAAG,KAAK;;;;;;YAEV,4BACC,8OAAC;gBACC,IAAI;gBACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gBACA,QAAQ,iBAAiB;gBAE3B,MAAM,QAAQ,UAAU;0BAEvB;;;;;;;;;;;;AAKX;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Badge.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { cn } from '@/lib/utils'\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-all duration-200',\n  {\n    variants: {\n      variant: {\n        default: 'bg-blue-100 text-blue-800 hover:bg-blue-200',\n        secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200',\n        destructive: 'bg-red-100 text-red-800 hover:bg-red-200',\n        success: 'bg-green-100 text-green-800 hover:bg-green-200',\n        warning: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',\n        outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50',\n        purple: 'bg-purple-100 text-purple-800 hover:bg-purple-200',\n        indigo: 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200',\n        pink: 'bg-pink-100 text-pink-800 hover:bg-pink-200',\n        orange: 'bg-orange-100 text-orange-800 hover:bg-orange-200',\n      },\n      size: {\n        default: 'px-2.5 py-0.5 text-xs',\n        sm: 'px-2 py-0.5 text-xs',\n        lg: 'px-3 py-1 text-sm',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nconst Badge = forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant, size, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(badgeVariants({ variant, size }), className)}\n        {...props}\n      />\n    )\n  }\n)\nBadge.displayName = 'Badge'\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,uGACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,QAAQ;QACV;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IACvC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAC/C,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/contact/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport Link from 'next/link'\nimport { \n  MailIcon,\n  PhoneIcon,\n  MapPinIcon,\n  ClockIcon,\n  MessageCircleIcon,\n  SendIcon,\n  CheckIcon,\n  HelpCircleIcon,\n  UsersIcon,\n  BuildingIcon\n} from 'lucide-react'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { Card } from '@/components/ui/Card'\nimport { Input } from '@/components/ui/Input'\nimport { Badge } from '@/components/ui/Badge'\n\nconst contactMethods = [\n  {\n    icon: MailIcon,\n    title: 'Email Support',\n    description: 'Get help via email',\n    contact: '<EMAIL>',\n    response: 'Within 24 hours',\n    color: 'from-blue-500 to-blue-600'\n  },\n  {\n    icon: MessageCircleIcon,\n    title: 'Live Chat',\n    description: 'Chat with our team',\n    contact: 'Available in app',\n    response: 'Instant response',\n    color: 'from-green-500 to-emerald-600'\n  },\n  {\n    icon: PhoneIcon,\n    title: 'Phone Support',\n    description: 'Enterprise customers',\n    contact: '+****************',\n    response: 'Business hours',\n    color: 'from-purple-500 to-purple-600'\n  }\n]\n\nconst offices = [\n  {\n    city: 'San Francisco',\n    address: '123 Tech Street, Suite 100\\nSan Francisco, CA 94105',\n    phone: '+****************',\n    email: '<EMAIL>'\n  },\n  {\n    city: 'New York',\n    address: '456 Business Ave, Floor 25\\nNew York, NY 10001',\n    phone: '+****************',\n    email: '<EMAIL>'\n  },\n  {\n    city: 'London',\n    address: '789 Innovation Lane\\nLondon, UK EC1A 1BB',\n    phone: '+44 20 7123 4567',\n    email: '<EMAIL>'\n  }\n]\n\nconst supportTypes = [\n  { icon: HelpCircleIcon, title: 'General Support', description: 'Questions about features and usage' },\n  { icon: UsersIcon, title: 'Sales Inquiry', description: 'Pricing and plan information' },\n  { icon: BuildingIcon, title: 'Enterprise', description: 'Custom solutions for large teams' },\n  { icon: MailIcon, title: 'Partnership', description: 'Business partnerships and integrations' }\n]\n\nexport default function ContactPage() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    company: '',\n    subject: '',\n    message: '',\n    type: 'general'\n  })\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [isSubmitted, setIsSubmitted] = useState(false)\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsSubmitting(true)\n    \n    // Simulate form submission\n    await new Promise(resolve => setTimeout(resolve, 2000))\n    \n    setIsSubmitting(false)\n    setIsSubmitted(true)\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    })\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">LifeManager</h1>\n            </Link>\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/\" className=\"text-gray-600 hover:text-gray-900 font-medium\">\n                Home\n              </Link>\n              <Link href=\"/about\" className=\"text-gray-600 hover:text-gray-900 font-medium\">\n                About\n              </Link>\n              <Link href=\"/features\" className=\"text-gray-600 hover:text-gray-900 font-medium\">\n                Features\n              </Link>\n              <Link href=\"/pricing\" className=\"text-gray-600 hover:text-gray-900 font-medium\">\n                Pricing\n              </Link>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <Badge variant=\"indigo\" size=\"lg\" className=\"mb-6\">\n              <MessageCircleIcon className=\"h-4 w-4 mr-2\" />\n              Get in Touch\n            </Badge>\n            <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n              We're Here to\n              <span className=\"text-blue-600\"> Help</span>\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed\">\n              Have questions about LifeManager? Need help getting started? Our friendly support team \n              is ready to assist you every step of the way.\n            </p>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Contact Methods */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            className=\"text-center mb-16\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Choose Your Preferred Contact Method\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              We offer multiple ways to get in touch based on your needs\n            </p>\n          </motion.div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16\">\n            {contactMethods.map((method, index) => (\n              <motion.div\n                key={method.title}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                whileHover={{ scale: 1.05 }}\n              >\n                <Card className=\"p-6 text-center h-full hover:shadow-lg transition-all duration-300\">\n                  <div className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r ${method.color} rounded-full mb-4`}>\n                    <method.icon className=\"h-8 w-8 text-white\" />\n                  </div>\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">{method.title}</h3>\n                  <p className=\"text-gray-600 mb-4\">{method.description}</p>\n                  <div className=\"space-y-2\">\n                    <div className=\"font-medium text-gray-900\">{method.contact}</div>\n                    <div className=\"text-sm text-gray-500\">{method.response}</div>\n                  </div>\n                </Card>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* Support Types */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {supportTypes.map((type, index) => (\n              <motion.div\n                key={type.title}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"bg-gray-50 p-4 rounded-lg text-center hover:bg-gray-100 transition-colors\"\n              >\n                <type.icon className=\"h-8 w-8 text-blue-600 mx-auto mb-2\" />\n                <h4 className=\"font-semibold text-gray-900 mb-1\">{type.title}</h4>\n                <p className=\"text-sm text-gray-600\">{type.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Form */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n            {/* Form */}\n            <motion.div\n              initial={{ opacity: 0, x: -50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n            >\n              <Card className=\"p-8\">\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Send us a Message</h2>\n                \n                {isSubmitted ? (\n                  <motion.div\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    className=\"text-center py-8\"\n                  >\n                    <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                      <CheckIcon className=\"h-8 w-8 text-green-600\" />\n                    </div>\n                    <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Message Sent!</h3>\n                    <p className=\"text-gray-600\">We'll get back to you within 24 hours.</p>\n                  </motion.div>\n                ) : (\n                  <form onSubmit={handleSubmit} className=\"space-y-6\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                      <Input\n                        label=\"Name\"\n                        name=\"name\"\n                        value={formData.name}\n                        onChange={handleInputChange}\n                        required\n                      />\n                      <Input\n                        label=\"Email\"\n                        name=\"email\"\n                        type=\"email\"\n                        value={formData.email}\n                        onChange={handleInputChange}\n                        required\n                      />\n                    </div>\n                    \n                    <Input\n                      label=\"Company (Optional)\"\n                      name=\"company\"\n                      value={formData.company}\n                      onChange={handleInputChange}\n                    />\n                    \n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        Inquiry Type\n                      </label>\n                      <select\n                        name=\"type\"\n                        value={formData.type}\n                        onChange={handleInputChange}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      >\n                        <option value=\"general\">General Support</option>\n                        <option value=\"sales\">Sales Inquiry</option>\n                        <option value=\"enterprise\">Enterprise</option>\n                        <option value=\"partnership\">Partnership</option>\n                      </select>\n                    </div>\n                    \n                    <Input\n                      label=\"Subject\"\n                      name=\"subject\"\n                      value={formData.subject}\n                      onChange={handleInputChange}\n                      required\n                    />\n                    \n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        Message\n                      </label>\n                      <textarea\n                        name=\"message\"\n                        value={formData.message}\n                        onChange={handleInputChange}\n                        rows={6}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\"\n                        placeholder=\"Tell us how we can help...\"\n                        required\n                      />\n                    </div>\n                    \n                    <Button\n                      type=\"submit\"\n                      size=\"lg\"\n                      className=\"w-full\"\n                      loading={isSubmitting}\n                      loadingText=\"Sending...\"\n                    >\n                      <SendIcon className=\"h-5 w-5 mr-2\" />\n                      Send Message\n                    </Button>\n                  </form>\n                )}\n              </Card>\n            </motion.div>\n\n            {/* Office Locations */}\n            <motion.div\n              initial={{ opacity: 0, x: 50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n            >\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Our Offices</h2>\n              <div className=\"space-y-6\">\n                {offices.map((office, index) => (\n                  <motion.div\n                    key={office.city}\n                    initial={{ opacity: 0, y: 20 }}\n                    whileInView={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.5, delay: index * 0.1 }}\n                    viewport={{ once: true }}\n                  >\n                    <Card className=\"p-6 hover:shadow-lg transition-all duration-300\">\n                      <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">{office.city}</h3>\n                      <div className=\"space-y-3\">\n                        <div className=\"flex items-start\">\n                          <MapPinIcon className=\"h-5 w-5 text-gray-400 mr-3 mt-0.5\" />\n                          <div className=\"text-gray-600 whitespace-pre-line\">{office.address}</div>\n                        </div>\n                        <div className=\"flex items-center\">\n                          <PhoneIcon className=\"h-5 w-5 text-gray-400 mr-3\" />\n                          <div className=\"text-gray-600\">{office.phone}</div>\n                        </div>\n                        <div className=\"flex items-center\">\n                          <MailIcon className=\"h-5 w-5 text-gray-400 mr-3\" />\n                          <div className=\"text-gray-600\">{office.email}</div>\n                        </div>\n                      </div>\n                    </Card>\n                  </motion.div>\n                ))}\n              </div>\n\n              {/* Business Hours */}\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5, delay: 0.3 }}\n                viewport={{ once: true }}\n                className=\"mt-8\"\n              >\n                <Card className=\"p-6 bg-blue-50 border-blue-200\">\n                  <div className=\"flex items-center mb-4\">\n                    <ClockIcon className=\"h-6 w-6 text-blue-600 mr-3\" />\n                    <h3 className=\"text-xl font-semibold text-gray-900\">Business Hours</h3>\n                  </div>\n                  <div className=\"space-y-2 text-gray-600\">\n                    <div className=\"flex justify-between\">\n                      <span>Monday - Friday</span>\n                      <span>9:00 AM - 6:00 PM</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Saturday</span>\n                      <span>10:00 AM - 4:00 PM</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Sunday</span>\n                      <span>Closed</span>\n                    </div>\n                  </div>\n                  <p className=\"text-sm text-gray-500 mt-4\">\n                    All times are in local timezone. Emergency support available 24/7 for Enterprise customers.\n                  </p>\n                </Card>\n              </motion.div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h3 className=\"text-2xl font-bold mb-4\">LifeManager</h3>\n            <div className=\"flex justify-center space-x-6 mb-6\">\n              <Link href=\"/\" className=\"text-gray-400 hover:text-white\">Home</Link>\n              <Link href=\"/about\" className=\"text-gray-400 hover:text-white\">About</Link>\n              <Link href=\"/features\" className=\"text-gray-400 hover:text-white\">Features</Link>\n              <Link href=\"/pricing\" className=\"text-gray-400 hover:text-white\">Pricing</Link>\n              <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white\">Privacy</Link>\n              <Link href=\"/terms\" className=\"text-gray-400 hover:text-white\">Terms</Link>\n            </div>\n            <p className=\"text-gray-500 text-sm\">\n              © 2024 LifeManager. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AACA;AApBA;;;;;;;;;;AAsBA,MAAM,iBAAiB;IACrB;QACE,MAAM,sMAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;QACV,OAAO;IACT;IACA;QACE,MAAM,4NAAA,CAAA,oBAAiB;QACvB,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;QACV,OAAO;IACT;IACA;QACE,MAAM,wMAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;QACV,OAAO;IACT;CACD;AAED,MAAM,UAAU;IACd;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;IACT;CACD;AAED,MAAM,eAAe;IACnB;QAAE,MAAM,kOAAA,CAAA,iBAAc;QAAE,OAAO;QAAmB,aAAa;IAAqC;IACpG;QAAE,MAAM,wMAAA,CAAA,YAAS;QAAE,OAAO;QAAiB,aAAa;IAA+B;IACvF;QAAE,MAAM,8MAAA,CAAA,eAAY;QAAE,OAAO;QAAc,aAAa;IAAmC;IAC3F;QAAE,MAAM,sMAAA,CAAA,WAAQ;QAAE,OAAO;QAAe,aAAa;IAAyC;CAC/F;AAEc,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,gBAAgB;QAChB,eAAe;IACjB;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAgD;;;;;;kDAGzE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAgD;;;;;;kDAG9E,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAgD;;;;;;kDAGjF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxF,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAS,MAAK;gCAAK,WAAU;;kDAC1C,8OAAC,4NAAA,CAAA,oBAAiB;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGhD,8OAAC;gCAAG,WAAU;;oCAAoD;kDAEhE,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC;gCAAE,WAAU;0CAA+D;;;;;;;;;;;;;;;;;;;;;;0BASlF,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,OAAO;oCAAK;8CAE1B,cAAA,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC;gDAAI,WAAW,CAAC,mEAAmE,EAAE,OAAO,KAAK,CAAC,kBAAkB,CAAC;0DACpH,cAAA,8OAAC,OAAO,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEzB,8OAAC;gDAAG,WAAU;0DAA4C,OAAO,KAAK;;;;;;0DACtE,8OAAC;gDAAE,WAAU;0DAAsB,OAAO,WAAW;;;;;;0DACrD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAA6B,OAAO,OAAO;;;;;;kEAC1D,8OAAC;wDAAI,WAAU;kEAAyB,OAAO,QAAQ;;;;;;;;;;;;;;;;;;mCAftD,OAAO,KAAK;;;;;;;;;;sCAuBvB,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAG,WAAU;sDAAoC,KAAK,KAAK;;;;;;sDAC5D,8OAAC;4CAAE,WAAU;sDAAyB,KAAK,WAAW;;;;;;;mCATjD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;0BAiBzB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;0CAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;wCAErD,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,wMAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAEvB,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;iEAG/B,8OAAC;4CAAK,UAAU;4CAAc,WAAU;;8DACtC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DACJ,OAAM;4DACN,MAAK;4DACL,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,QAAQ;;;;;;sEAEV,8OAAC,iIAAA,CAAA,QAAK;4DACJ,OAAM;4DACN,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,QAAQ;;;;;;;;;;;;8DAIZ,8OAAC,iIAAA,CAAA,QAAK;oDACJ,OAAM;oDACN,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU;;;;;;8DAGZ,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAU;;;;;;8EACxB,8OAAC;oEAAO,OAAM;8EAAQ;;;;;;8EACtB,8OAAC;oEAAO,OAAM;8EAAa;;;;;;8EAC3B,8OAAC;oEAAO,OAAM;8EAAc;;;;;;;;;;;;;;;;;;8DAIhC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,OAAM;oDACN,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,QAAQ;;;;;;8DAGV,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,MAAM;4DACN,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAIZ,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,MAAK;oDACL,WAAU;oDACV,SAAS;oDACT,aAAY;;sEAEZ,8OAAC,sMAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;0CAS/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO,QAAQ;gDAAI;gDAChD,UAAU;oDAAE,MAAM;gDAAK;0DAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;oDAAC,WAAU;;sEACd,8OAAC;4DAAG,WAAU;sEAA4C,OAAO,IAAI;;;;;;sEACrE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,8MAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;sFACtB,8OAAC;4EAAI,WAAU;sFAAqC,OAAO,OAAO;;;;;;;;;;;;8EAEpE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,wMAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;sFACrB,8OAAC;4EAAI,WAAU;sFAAiB,OAAO,KAAK;;;;;;;;;;;;8EAE9C,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,sMAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,8OAAC;4EAAI,WAAU;sFAAiB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;+CAnB7C,OAAO,IAAI;;;;;;;;;;kDA4BtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,wMAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,8OAAC;4DAAG,WAAU;sEAAsC;;;;;;;;;;;;8DAEtD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;8DAGV,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWtD,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAiC;;;;;;kDAC1D,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAiC;;;;;;kDAC/D,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAiC;;;;;;kDAClE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAiC;;;;;;kDACjE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAiC;;;;;;kDACjE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAiC;;;;;;;;;;;;0CAEjE,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}]}