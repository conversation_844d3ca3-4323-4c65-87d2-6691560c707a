{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/supabase/middleware.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { NextResponse, type NextRequest } from 'next/server'\n\nexport async function updateSession(request: NextRequest) {\n  let supabaseResponse = NextResponse.next({\n    request,\n  })\n\n  const supabase = createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return request.cookies.getAll()\n        },\n        setAll(cookiesToSet) {\n          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))\n          supabaseResponse = NextResponse.next({\n            request,\n          })\n          cookiesToSet.forEach(({ name, value, options }) =>\n            supabaseResponse.cookies.set(name, value, options)\n          )\n        },\n      },\n    }\n  )\n\n  // IMPORTANT: Avoid writing any logic between createServerClient and\n  // supabase.auth.getUser(). A simple mistake could make it very hard to debug\n  // issues with users being randomly logged out.\n\n  const {\n    data: { user },\n  } = await supabase.auth.getUser()\n\n  if (\n    !user &&\n    !request.nextUrl.pathname.startsWith('/login') &&\n    !request.nextUrl.pathname.startsWith('/signup') &&\n    !request.nextUrl.pathname.startsWith('/auth')\n  ) {\n    // no user, potentially respond by redirecting the user to the login page\n    const url = request.nextUrl.clone()\n    url.pathname = '/login'\n    return NextResponse.redirect(url)\n  }\n\n  // IMPORTANT: You *must* return the supabaseResponse object as it is. If you're\n  // creating a new response object with NextResponse.next() make sure to:\n  // 1. Pass the request in it, like so:\n  //    const myNewResponse = NextResponse.next({ request })\n  // 2. Copy over the cookies, like so:\n  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())\n  // 3. Change the myNewResponse object instead of the supabaseResponse object\n\n  return supabaseResponse\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAEO,eAAe,cAAc,OAAoB;IACtD,IAAI,mBAAmB,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvC;IACF;IAEA,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,qBAAkB,AAAD,sUAGhC;QACE,SAAS;YACP;gBACE,OAAO,QAAQ,OAAO,CAAC,MAAM;YAC/B;YACA,QAAO,YAAY;gBACjB,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAAK,QAAQ,OAAO,CAAC,GAAG,CAAC,MAAM;gBAC7E,mBAAmB,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACnC;gBACF;gBACA,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,iBAAiB,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO;YAE9C;QACF;IACF;IAGF,oEAAoE;IACpE,6EAA6E;IAC7E,+CAA+C;IAE/C,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IACE,CAAC,QACD,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,aACrC,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,cACrC,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,UACrC;QACA,yEAAyE;QACzE,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;QACjC,IAAI,QAAQ,GAAG;QACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,+EAA+E;IAC/E,wEAAwE;IACxE,sCAAsC;IACtC,0DAA0D;IAC1D,qCAAqC;IACrC,qEAAqE;IACrE,4EAA4E;IAE5E,OAAO;AACT"}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n/request.ts"], "sourcesContent": ["import { getRequestConfig } from 'next-intl/server'\nimport { locales, defaultLocale } from './config'\n\nexport default getRequestConfig(async ({ locale }) => {\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale as any)) {\n    locale = defaultLocale\n  }\n\n  return {\n    messages: (await import(`./messages/${locale}.json`)).default,\n    timeZone: 'UTC',\n    now: new Date(),\n    formats: {\n      dateTime: {\n        short: {\n          day: 'numeric',\n          month: 'short',\n          year: 'numeric'\n        },\n        long: {\n          day: 'numeric',\n          month: 'long',\n          year: 'numeric',\n          hour: 'numeric',\n          minute: 'numeric'\n        }\n      },\n      number: {\n        currency: {\n          style: 'currency',\n          currency: 'USD'\n        },\n        percent: {\n          style: 'percent',\n          minimumFractionDigits: 0,\n          maximumFractionDigits: 2\n        }\n      }\n    }\n  }\n})\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAEe,CAAA,GAAA,kQAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,yDAAyD;IACzD,IAAI,CAAC,6HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,SAAgB;QACpC,SAAS,6HAAA,CAAA,gBAAa;IACxB;IAEA,OAAO;QACL,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;kBAAa,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;QAC7D,UAAU;QACV,KAAK,IAAI;QACT,SAAS;YACP,UAAU;gBACR,OAAO;oBACL,KAAK;oBACL,OAAO;oBACP,MAAM;gBACR;gBACA,MAAM;oBACJ,KAAK;oBACL,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,QAAQ;gBACV;YACF;YACA,QAAQ;gBACN,UAAU;oBACR,OAAO;oBACP,UAAU;gBACZ;gBACA,SAAS;oBACP,OAAO;oBACP,uBAAuB;oBACvB,uBAAuB;gBACzB;YACF;QACF;IACF;AACF"}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n/config.ts"], "sourcesContent": ["import { createSharedPathnamesNavigation } from 'next-intl/navigation'\n\nexport const locales = ['en', 'es', 'fr', 'de', 'ja', 'zh'] as const\nexport type Locale = (typeof locales)[number]\n\nexport const defaultLocale: Locale = 'en'\n\nexport const localeNames: Record<Locale, string> = {\n  en: 'English',\n  es: 'Español',\n  fr: 'Français', \n  de: 'Deutsch',\n  ja: '日本語',\n  zh: '中文'\n}\n\nexport const localeFlags: Record<Locale, string> = {\n  en: '🇺🇸',\n  es: '🇪🇸',\n  fr: '🇫🇷',\n  de: '🇩🇪', \n  ja: '🇯🇵',\n  zh: '🇨🇳'\n}\n\nexport const { Link, redirect, usePathname, useRouter } = createSharedPathnamesNavigation({\n  locales,\n  pathnames: {\n    '/': '/',\n    '/about': {\n      en: '/about',\n      es: '/acerca-de',\n      fr: '/a-propos',\n      de: '/uber-uns',\n      ja: '/about',\n      zh: '/about'\n    },\n    '/features': {\n      en: '/features',\n      es: '/caracteristicas',\n      fr: '/fonctionnalites',\n      de: '/funktionen',\n      ja: '/features',\n      zh: '/features'\n    },\n    '/pricing': {\n      en: '/pricing',\n      es: '/precios',\n      fr: '/tarifs',\n      de: '/preise',\n      ja: '/pricing',\n      zh: '/pricing'\n    },\n    '/contact': {\n      en: '/contact',\n      es: '/contacto',\n      fr: '/contact',\n      de: '/kontakt',\n      ja: '/contact',\n      zh: '/contact'\n    },\n    '/privacy': {\n      en: '/privacy',\n      es: '/privacidad',\n      fr: '/confidentialite',\n      de: '/datenschutz',\n      ja: '/privacy',\n      zh: '/privacy'\n    },\n    '/terms': {\n      en: '/terms',\n      es: '/terminos',\n      fr: '/conditions',\n      de: '/bedingungen',\n      ja: '/terms',\n      zh: '/terms'\n    }\n  }\n})\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAEO,MAAM,UAAU;IAAC;IAAM;IAAM;IAAM;IAAM;IAAM;CAAK;AAGpD,MAAM,gBAAwB;AAE9B,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,oNAAA,CAAA,kCAA+B,AAAD,EAAE;IACxF;IACA,WAAW;QACT,KAAK;QACL,UAAU;YACR,IAAI;YACJ,IAAI;YACJ,IAA<PERSON>;Y<PERSON><PERSON>,IAAI;YAC<PERSON>,IAAI;YACJ,IAA<PERSON>;QACN;QACA,aAAa;YACX,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,YAAY;YACV,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,YAAY;YACV,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,YAAY;YACV,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;YACR,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;AACF"}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { updateSession } from '@/lib/supabase/middleware'\nimport createIntlMiddleware from 'next-intl/middleware'\nimport { locales, defaultLocale } from '@/i18n/config'\n\nconst intlMiddleware = createIntlMiddleware({\n  locales,\n  defaultLocale,\n  localePrefix: 'as-needed'\n})\n\nexport async function middleware(request: any) {\n  // Handle internationalization first\n  const intlResponse = intlMiddleware(request)\n\n  // Then handle Supabase session\n  const supabaseResponse = await updateSession(request)\n\n  // Return the intl response if it exists (redirect), otherwise supabase response\n  return intlResponse || supabaseResponse\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * Feel free to modify this pattern to include more paths.\n     */\n    '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8LAAA,CAAA,UAAoB,AAAD,EAAE;IAC1C,SAAA,6HAAA,CAAA,UAAO;IACP,eAAA,6HAAA,CAAA,gBAAa;IACb,cAAc;AAChB;AAEO,eAAe,WAAW,OAAY;IAC3C,oCAAoC;IACpC,MAAM,eAAe,eAAe;IAEpC,+BAA+B;IAC/B,MAAM,mBAAmB,MAAM,CAAA,GAAA,4IAAA,CAAA,gBAAa,AAAD,EAAE;IAE7C,gFAAgF;IAChF,OAAO,gBAAgB;AACzB;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}