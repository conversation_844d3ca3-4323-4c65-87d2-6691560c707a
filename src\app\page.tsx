'use client'

import Link from 'next/link'
import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { redirect } from 'next/navigation'
import { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion'
import { <PERSON><PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import TestimonialsCarousel from '@/components/ui/TestimonialsCarousel'
import PricingSection from '@/components/ui/PricingSection'
import GlobalStatsSection from '@/components/ui/GlobalStatsSection'
import { CompactLanguageSelector } from '@/components/ui/LanguageSelector'
import {
  CheckSquareIcon,
  DollarSignIcon,
  ShoppingCartIcon,
  MessageCircleIcon,
  ChefHatIcon,
  ArrowRightIcon,
  StarIcon,
  GlobeIcon,
  ShieldCheckIcon,
  ZapIcon,
  TrendingUpIcon,
  UsersIcon,
  CheckIcon,
  PlayIcon,
  SmartphoneIcon,
  LaptopIcon,
  TabletIcon,
  SparklesIcon,
  RocketIcon,
  HeartIcon,
  BrainIcon,
  ClockIcon,
  TargetIcon,
  ChevronDownIcon,
  MouseIcon
} from 'lucide-react'

// Typing Animation Component
const TypingAnimation = ({ words, className = "" }: { words: string[], className?: string }) => {
  const [currentWordIndex, setCurrentWordIndex] = useState(0)
  const [currentText, setCurrentText] = useState('')
  const [isDeleting, setIsDeleting] = useState(false)

  useEffect(() => {
    const word = words[currentWordIndex]
    const timeout = setTimeout(() => {
      if (!isDeleting) {
        if (currentText.length < word.length) {
          setCurrentText(word.slice(0, currentText.length + 1))
        } else {
          setTimeout(() => setIsDeleting(true), 2000)
        }
      } else {
        if (currentText.length > 0) {
          setCurrentText(currentText.slice(0, -1))
        } else {
          setIsDeleting(false)
          setCurrentWordIndex((prev) => (prev + 1) % words.length)
        }
      }
    }, isDeleting ? 50 : 100)

    return () => clearTimeout(timeout)
  }, [currentText, isDeleting, currentWordIndex, words])

  return (
    <span className={className}>
      {currentText}
      <motion.span
        animate={{ opacity: [1, 0] }}
        transition={{ duration: 0.8, repeat: Infinity, repeatType: "reverse" }}
        className="inline-block w-0.5 h-8 bg-blue-600 ml-1"
      />
    </span>
  )
}

// Floating Elements Component
const FloatingElements = () => {
  const floatingIcons = [
    { Icon: CheckSquareIcon, delay: 0, x: "10%", y: "20%" },
    { Icon: DollarSignIcon, delay: 0.5, x: "85%", y: "15%" },
    { Icon: ShoppingCartIcon, delay: 1, x: "15%", y: "70%" },
    { Icon: BrainIcon, delay: 1.5, x: "80%", y: "75%" },
    { Icon: ChefHatIcon, delay: 2, x: "50%", y: "10%" },
    { Icon: ShieldCheckIcon, delay: 2.5, x: "90%", y: "45%" }
  ]

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {floatingIcons.map(({ Icon, delay, x, y }, index) => (
        <motion.div
          key={index}
          className="absolute"
          style={{ left: x, top: y }}
          initial={{ opacity: 0, scale: 0 }}
          animate={{
            opacity: [0, 0.6, 0],
            scale: [0, 1, 0],
            y: [0, -20, 0]
          }}
          transition={{
            duration: 4,
            delay,
            repeat: Infinity,
            repeatType: "loop"
          }}
        >
          <Icon className="h-8 w-8 text-blue-400" />
        </motion.div>
      ))}
    </div>
  )
}

// Scroll Indicator Component
const ScrollIndicator = () => {
  return (
    <motion.div
      className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center text-gray-600"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 2, duration: 0.8 }}
    >
      <motion.div
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity, repeatType: "loop" }}
        className="flex flex-col items-center"
      >
        <span className="text-sm mb-2">Scroll to explore</span>
        <ChevronDownIcon className="h-5 w-5" />
      </motion.div>
    </motion.div>
  )
}

export default function Home() {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()
  const { scrollY } = useScroll()
  const heroY = useTransform(scrollY, [0, 500], [0, -150])
  const heroOpacity = useTransform(scrollY, [0, 300], [1, 0])

  useEffect(() => {
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        window.location.href = '/dashboard'
      }
      setUser(user)
      setLoading(false)
    }
    checkUser()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-white text-center"
        >
          <SparklesIcon className="h-16 w-16 mx-auto mb-4 animate-pulse" />
          <p className="text-xl">Loading LifeManager...</p>
        </motion.div>
      </div>
    )
  }

  const features = [
    {
      name: 'Smart Task Management',
      description: 'AI-powered task organization with drag-and-drop, priorities, and intelligent scheduling.',
      icon: CheckSquareIcon,
      color: 'from-blue-500 to-blue-600',
      stats: '10M+ tasks completed',
    },
    {
      name: 'Advanced Budget Tracking',
      description: 'Real-time expense monitoring with predictive analytics and automated categorization.',
      icon: DollarSignIcon,
      color: 'from-green-500 to-emerald-600',
      stats: '$2B+ tracked globally',
    },
    {
      name: 'Intelligent Shopping',
      description: 'Smart shopping lists with price tracking, store optimization, and family sharing.',
      icon: ShoppingCartIcon,
      color: 'from-purple-500 to-purple-600',
      stats: '500K+ lists created',
    },
    {
      name: 'AI Life Assistant',
      description: 'Personalized recommendations and insights powered by advanced machine learning.',
      icon: BrainIcon,
      color: 'from-indigo-500 to-indigo-600',
      stats: '1M+ decisions optimized',
    },
    {
      name: 'Recipe Intelligence',
      description: 'Auto-import recipes, nutritional analysis, and meal planning with dietary preferences.',
      icon: ChefHatIcon,
      color: 'from-orange-500 to-red-500',
      stats: '100K+ recipes saved',
    },
    {
      name: 'Global Sync & Security',
      description: 'End-to-end encryption with real-time sync across all your devices worldwide.',
      icon: ShieldCheckIcon,
      color: 'from-teal-500 to-cyan-600',
      stats: '99.9% uptime guarantee',
    },
  ]

  const testimonials = [
    {
      name: 'Sarah Chen',
      role: 'Product Manager',
      location: 'San Francisco, USA',
      image: '/api/placeholder/64/64',
      content: 'LifeManager transformed how I organize my life. The AI suggestions are incredibly accurate and have saved me hours every week.',
      rating: 5,
    },
    {
      name: 'Marcus Johnson',
      role: 'Entrepreneur',
      location: 'London, UK',
      image: '/api/placeholder/64/64',
      content: 'As someone juggling multiple businesses, LifeManager keeps me on track. The budget tracking alone has saved me thousands.',
      rating: 5,
    },
    {
      name: 'Yuki Tanaka',
      role: 'Software Engineer',
      location: 'Tokyo, Japan',
      image: '/api/placeholder/64/64',
      content: 'The cross-platform sync is flawless. I can start a task on my phone and finish it on my laptop seamlessly.',
      rating: 5,
    },
    {
      name: 'Emma Rodriguez',
      role: 'Marketing Director',
      location: 'Barcelona, Spain',
      image: '/api/placeholder/64/64',
      content: 'The recipe management feature is a game-changer! I can import recipes from any website and plan my meals effortlessly.',
      rating: 5,
    },
    {
      name: 'David Kim',
      role: 'Freelance Designer',
      location: 'Seoul, South Korea',
      image: '/api/placeholder/64/64',
      content: 'I love how everything is connected. My shopping lists sync with my budget, and the AI chat helps me make better decisions.',
      rating: 5,
    },
    {
      name: 'Lisa Thompson',
      role: 'Working Mom',
      location: 'Toronto, Canada',
      image: '/api/placeholder/64/64',
      content: 'Managing a family and career is tough, but LifeManager makes it so much easier. The mobile app is perfect for on-the-go planning.',
      rating: 5,
    },
  ]





  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white/95 backdrop-blur-sm shadow-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <motion.div
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6 }}
                className="bg-gradient-to-r from-blue-500 to-purple-600 p-2 rounded-full mr-3"
              >
                <RocketIcon className="h-6 w-6 text-white" />
              </motion.div>
              <h1 className="text-2xl font-bold text-gray-900">LifeManager</h1>
            </div>

            <div className="flex items-center space-x-4">
              <nav className="hidden md:flex items-center space-x-6">
                <Link href="/about" className="text-gray-600 hover:text-gray-900 font-medium transition-colors">
                  About
                </Link>
                <Link href="/features" className="text-gray-600 hover:text-gray-900 font-medium transition-colors">
                  Features
                </Link>
                <Link href="/pricing" className="text-gray-600 hover:text-gray-900 font-medium transition-colors">
                  Pricing
                </Link>
                <Link href="/contact" className="text-gray-600 hover:text-gray-900 font-medium transition-colors">
                  Contact
                </Link>
              </nav>

              <CompactLanguageSelector />

              <div className="flex items-center space-x-3">
                <Button variant="outline" size="sm">
                  <Link href="/login">Sign In</Link>
                </Button>
                <Button size="sm">
                  <Link href="/signup">Get Started</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <motion.section
        className="relative py-20 min-h-screen flex items-center overflow-hidden"
        style={{ y: heroY, opacity: heroOpacity }}
      >
        <FloatingElements />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mb-6"
          >
            <Badge variant="purple" size="lg" className="mb-4 animate-pulse">
              <SparklesIcon className="h-4 w-4 mr-2" />
              AI-Powered Life Management
            </Badge>
          </motion.div>

          <motion.h1
            className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            Manage Your Life{' '}
            <br className="hidden sm:block" />
            <TypingAnimation
              words={["Effortlessly", "Intelligently", "Seamlessly", "Efficiently"]}
              className="text-blue-600"
            />
          </motion.h1>

          <motion.p
            className="text-xl md:text-2xl text-gray-600 mb-8 max-w-4xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            The all-in-one platform to organize your tasks, track your budget,
            manage shopping lists, and get AI-powered assistance for better life management.
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button size="xl" className="group">
                <Link href="/signup" className="flex items-center">
                  Start Free Today
                  <motion.div
                    className="ml-2"
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    <ArrowRightIcon className="h-5 w-5" />
                  </motion.div>
                </Link>
              </Button>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button variant="outline" size="xl">
                <Link href="/login" className="flex items-center">
                  <PlayIcon className="h-5 w-5 mr-2" />
                  Watch Demo
                </Link>
              </Button>
            </motion.div>
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            className="flex flex-wrap justify-center items-center gap-8 text-gray-500"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.2 }}
          >
            <div className="flex items-center">
              <UsersIcon className="h-5 w-5 mr-2" />
              <span className="text-sm font-medium">2.5M+ Users</span>
            </div>
            <div className="flex items-center">
              <StarIcon className="h-5 w-5 mr-2 text-yellow-400 fill-current" />
              <span className="text-sm font-medium">4.9/5 Rating</span>
            </div>
            <div className="flex items-center">
              <ShieldCheckIcon className="h-5 w-5 mr-2 text-green-500" />
              <span className="text-sm font-medium">Enterprise Security</span>
            </div>
          </motion.div>
        </div>

        <ScrollIndicator />
      </motion.section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything You Need in One Place
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Streamline your daily life with our comprehensive suite of management tools.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{
                  scale: 1.05,
                  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                }}
                className="group"
              >
                <Card className="h-full p-6 cursor-pointer border-2 border-transparent hover:border-blue-200 transition-all duration-300">
                  <div className="flex items-center mb-4">
                    <motion.div
                      className={`p-3 rounded-xl bg-gradient-to-r ${feature.color} text-white`}
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.6 }}
                    >
                      <feature.icon className="h-6 w-6" />
                    </motion.div>
                    <h3 className="text-xl font-semibold text-gray-900 ml-4 group-hover:text-blue-600 transition-colors">
                      {feature.name}
                    </h3>
                  </div>
                  <p className="text-gray-600 mb-4 leading-relaxed">{feature.description}</p>
                  <div className="flex items-center justify-between">
                    <Badge variant="secondary" className="text-xs">
                      {feature.stats}
                    </Badge>
                    <motion.div
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                      whileHover={{ x: 5 }}
                    >
                      <ArrowRightIcon className="h-4 w-4 text-blue-600" />
                    </motion.div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Carousel */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Loved by Users Worldwide
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              See what our community has to say about transforming their daily lives.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <TestimonialsCarousel
              testimonials={testimonials}
              autoPlay={true}
              autoPlayInterval={6000}
              showControls={true}
              showIndicators={true}
            />
          </motion.div>

          {/* Additional testimonials grid for more social proof */}
          <motion.div
            className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-6"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            {testimonials.slice(0, 3).map((testimonial, index) => (
              <motion.div
                key={`grid-${testimonial.name}`}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.02 }}
                className="group"
              >
                <Card className="p-4 text-center hover:shadow-lg transition-all duration-300 bg-white/70 backdrop-blur-sm">
                  <div className="flex justify-center mb-3">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <StarIcon key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <blockquote className="text-gray-700 text-sm mb-4 italic">
                    "{testimonial.content.slice(0, 100)}..."
                  </blockquote>
                  <div className="flex items-center justify-center">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xs mr-3">
                      {testimonial.name.split(' ').map(n => n[0]).join('')}
                    </div>
                    <div className="text-left">
                      <div className="font-semibold text-gray-900 text-sm">{testimonial.name}</div>
                      <div className="text-xs text-gray-600">{testimonial.role}</div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Global Stats Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Trusted Globally
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Join millions of users who have transformed their productivity and life management.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <GlobalStatsSection />
          </motion.div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Choose Your Plan
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Start free and upgrade as you grow. All plans include our core features.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <PricingSection />
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Ready to Take Control?
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Join thousands of users who have transformed their daily routines with LifeManager.
            </p>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button size="xl" variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100">
                <Link href="/signup" className="flex items-center">
                  Get Started Free
                  <motion.div
                    className="ml-2"
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    <ArrowRightIcon className="h-5 w-5" />
                  </motion.div>
                </Link>
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center justify-center mb-6">
              <motion.div
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6 }}
                className="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-full mr-4"
              >
                <RocketIcon className="h-8 w-8 text-white" />
              </motion.div>
              <h3 className="text-3xl font-bold">LifeManager</h3>
            </div>
            <p className="text-gray-400 mb-8 text-lg max-w-2xl mx-auto">
              Simplifying life management, one task at a time. Join the revolution of organized living.
            </p>

            <div className="flex flex-wrap justify-center gap-8 mb-8 text-sm">
              <Link href="/about" className="text-gray-400 hover:text-white transition-colors">About</Link>
              <Link href="/features" className="text-gray-400 hover:text-white transition-colors">Features</Link>
              <Link href="/pricing" className="text-gray-400 hover:text-white transition-colors">Pricing</Link>
              <Link href="/contact" className="text-gray-400 hover:text-white transition-colors">Contact</Link>
              <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors">Privacy</Link>
              <Link href="/terms" className="text-gray-400 hover:text-white transition-colors">Terms</Link>
            </div>

            <div className="border-t border-gray-800 pt-8">
              <p className="text-gray-500 text-sm">
                © 2024 LifeManager. All rights reserved. Made with ❤️ for productivity enthusiasts worldwide.
              </p>
            </div>
          </motion.div>
        </div>
      </footer>
    </div>
  )
}
